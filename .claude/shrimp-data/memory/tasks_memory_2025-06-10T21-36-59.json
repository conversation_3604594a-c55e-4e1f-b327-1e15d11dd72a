{"tasks": [{"id": "b07efa9b-77b4-4315-9392-0612c250522a", "name": "Create Provider Factory System", "description": "Implement a unified provider factory system to eliminate scattered provider matching logic across multiple modules. Replace hardcoded match statements in main.rs, app.rs, llm/mod.rs, and config/mod.rs with a centralized factory pattern.", "notes": "This eliminates 5+ scattered match statements and centralizes provider creation logic. Essential for maintaining consistency as new providers are added.", "status": "completed", "dependencies": [], "createdAt": "2025-06-03T06:57:17.195Z", "updatedAt": "2025-06-03T07:10:45.119Z", "relatedFiles": [{"path": "src/llm/factory.rs", "type": "CREATE", "description": "New provider factory implementation with ProviderBuilder trait and concrete builders"}, {"path": "src/llm/mod.rs", "type": "TO_MODIFY", "description": "Updated to use factory pattern, removed hardcoded provider matching", "lineStart": 135, "lineEnd": 175}, {"path": "src/config/mod.rs", "type": "TO_MODIFY", "description": "Updated validation to use factory, added parse_model_string function", "lineStart": 340, "lineEnd": 370}, {"path": "src/main.rs", "type": "TO_MODIFY", "description": "Removed parse_model_string function, now imports from config", "lineStart": 15, "lineEnd": 20}, {"path": "src/app.rs", "type": "TO_MODIFY", "description": "Updated create_agent_core_with_tool_support to use factory validation", "lineStart": 70, "lineEnd": 90}], "implementationGuide": "1. Create src/llm/factory.rs with ProviderFactory struct\\n2. Implement ProviderBuilder trait for each provider type\\n3. Register builders in factory: AnthropicBuilder, OpenRouterBuilder\\n4. Replace scattered match statements with factory.create_provider() calls\\n5. Maintain backward compatibility by preserving existing APIs\\n\\nKey interfaces:\\n```rust\\npub trait ProviderBuilder: Send + Sync {\\n    async fn build(&self, config: &LLMConfig) -> Result<Arc<dyn LLMProvider>>;\\n    fn validate_config(&self, config: &LLMConfig) -> Result<()>;\\n}\\n\\npub struct ProviderFactory {\\n    builders: HashMap<String, Box<dyn ProviderBuilder>>,\\n}\\n```", "verificationCriteria": "Factory correctly creates provider instances for all supported providers (anthropic, openrouter), validates configurations, maintains backward compatibility with existing create_llm_provider usage, and eliminates all hardcoded provider match statements.", "analysisResult": "Comprehensive refactoring plan to transform AutoRun CLI from organic growth to maintainable architecture. Eliminates hard-coded values, separates UI from core logic, establishes proper provider abstractions, and implements missing features while maintaining backward compatibility. The refactoring addresses scattered provider matching logic, massive app.rs file mixing concerns, numerous TODO items, and configuration chaos through systematic architectural patterns.", "summary": "Successfully implemented provider factory system. Created src/llm/factory.rs with ProviderFactory struct and ProviderBuilder trait. Replaced hardcoded match statements in create_llm_provider() and validate_provider() functions with factory.create_provider() calls. Updated config module to use factory validation and moved parse_model_string from main.rs. Updated app.rs to use factory validation in create_agent_core_with_tool_support(). Factory correctly creates provider instances for anthropic and openrouter, validates configurations, maintains backward compatibility with existing create_llm_provider usage, and eliminated hardcoded provider match statements. All 5+ scattered match statements have been replaced with centralized factory pattern.", "completedAt": "2025-06-03T07:10:45.119Z"}, {"id": "6e7c3783-3754-4636-a01a-3376c8d9a76d", "name": "Implement Configuration Registry", "description": "Create a unified configuration registry to replace hardcoded values and scattered configuration logic. Centralize model mappings, provider defaults, and configuration validation in a single, maintainable system.", "notes": "Replaces hardcoded model parsing and provider detection with data-driven approach. Makes adding new models/providers declarative rather than requiring code changes.", "status": "completed", "dependencies": [{"taskId": "b07efa9b-77b4-4315-9392-0612c250522a"}], "createdAt": "2025-06-03T06:57:17.195Z", "updatedAt": "2025-06-05T12:15:34.503Z", "relatedFiles": [{"path": "src/config/registry.rs", "type": "CREATE", "description": "New configuration registry implementation"}, {"path": "src/config/mod.rs", "type": "TO_MODIFY", "description": "Update detect_provider_and_key to use registry", "lineStart": 263, "lineEnd": 284}, {"path": "src/main.rs", "type": "TO_MODIFY", "description": "Remove hardcoded parse_model_string", "lineStart": 21, "lineEnd": 36}, {"path": "src/llm/mod.rs", "type": "TO_MODIFY", "description": "Update model capability detection", "lineStart": 236, "lineEnd": 301}], "implementationGuide": "1. Create src/config/registry.rs with ConfigRegistry struct\\n2. Define ModelInfo and ProviderConfig structs for metadata\\n3. Implement model mapping system replacing hardcoded parse_model_string logic\\n4. Add configuration validation and default value management\\n5. Update Config::default() to use registry for smart detection\\n\\nKey structures:\\n```rust\\n#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]\\npub struct ModelInfo {\\n    pub provider: String,\\n    pub canonical_name: String,\\n    pub capabilities: Vec<ModelCapability>,\\n    pub tool_support: ToolSupport,\\n}\\n\\npub struct ConfigRegistry {\\n    model_mappings: HashMap<String, ModelInfo>,\\n    provider_configs: HashMap<String, ProviderConfig>,\\n}\\n```", "verificationCriteria": "Registry correctly maps model names to providers, maintains all existing model compatibility, provides accurate capability detection, validates configurations, and eliminates hardcoded model parsing logic while preserving backward compatibility.", "analysisResult": "Comprehensive refactoring plan to transform AutoRun CLI from organic growth to maintainable architecture. Eliminates hard-coded values, separates UI from core logic, establishes proper provider abstractions, and implements missing features while maintaining backward compatibility. The refactoring addresses scattered provider matching logic, massive app.rs file mixing concerns, numerous TODO items, and configuration chaos through systematic architectural patterns.", "summary": "Successfully implemented ConfigRegistry with centralized model mappings, provider configuration, and validation. Replaced hardcoded values with data-driven approach while maintaining backward compatibility.", "completedAt": "2025-06-05T12:15:34.503Z"}, {"id": "8ee1b654-7908-4c83-a958-ad9249ae5029", "name": "Extract UI Components from App Core", "description": "Separate UI concerns from business logic in the massive app.rs file (1752 lines). Extract TUI-specific code into dedicated UI modules while preserving application state management and event handling.", "notes": "Critical for testability and maintainability. Business logic becomes testable without TUI dependencies. Prepares for potential future UI alternatives (web, CLI-only modes).", "status": "completed", "dependencies": [{"taskId": "b07efa9b-77b4-4315-9392-0612c250522a"}], "createdAt": "2025-06-03T06:57:17.195Z", "updatedAt": "2025-06-05T12:32:54.876Z", "relatedFiles": [{"path": "src/app.rs", "type": "TO_MODIFY", "description": "Split into core and UI concerns", "lineStart": 1, "lineEnd": 1752}, {"path": "src/core/app_core.rs", "type": "CREATE", "description": "Business logic extraction"}, {"path": "src/ui/app_interface.rs", "type": "CREATE", "description": "TUI interface implementation"}, {"path": "src/ui/renderer.rs", "type": "TO_MODIFY", "description": "Extract rendering methods", "lineStart": 1, "lineEnd": 2}, {"path": "src/core/mod.rs", "type": "CREATE", "description": "Core module organization"}], "implementationGuide": "1. Create src/ui/app_interface.rs for TUI-specific logic\\n2. Extract rendering methods from app.rs into src/ui/renderer.rs\\n3. Create src/core/app_core.rs for business logic only\\n4. Implement AppCore struct with agent management, message handling\\n5. Update App struct to compose AppCore + TuiInterface\\n6. Maintain existing event handling but separate concerns\\n\\nCore separation:\\n```rust\\n// Business logic only\\npub struct AppCore {\\n    agent_core: Option<AgentCore>,\\n    messages: Vec<String>,\\n    command_registry: Arc<CommandRegistry>,\\n    tool_executor: Arc<ToolExecutor>,\\n}\\n\\n// UI logic only\\npub struct TuiInterface {\\n    app_core: Arc<Mutex<AppCore>>,\\n    input_textarea: TextArea<'static>,\\n    completion_popup: CompletionPopup,\\n    widget_registry: Arc<WidgetRegistry>,\\n}\\n```", "verificationCriteria": "App functionality remains identical, UI rendering works correctly, business logic is testable in isolation, event handling preserved, existing APIs maintained, and code is properly separated by concern while reducing app.rs size significantly.", "analysisResult": "Comprehensive refactoring plan to transform AutoRun CLI from organic growth to maintainable architecture. Eliminates hard-coded values, separates UI from core logic, establishes proper provider abstractions, and implements missing features while maintaining backward compatibility. The refactoring addresses scattered provider matching logic, massive app.rs file mixing concerns, numerous TODO items, and configuration chaos through systematic architectural patterns.", "summary": "Successfully extracted UI components from massive app.rs into separated modules. Created AppCore for business logic, TuiInterface for UI concerns, and Renderer for rendering logic. Maintained app functionality while improving architecture testability.", "completedAt": "2025-06-05T12:32:54.875Z"}, {"id": "d6f4dfcf-644a-44da-9034-ed8dd5527a68", "name": "Implement Command Handler Pattern", "description": "Replace numerous TODO implementations in main.rs with proper command handler pattern. Create structured command processing for config, MC<PERSON>, doctor, and other CLI subcommands.", "notes": "Eliminates 15+ TODO items in main.rs and establishes extensible command processing. Each handler can be tested independently and new commands easily added.", "status": "completed", "dependencies": [{"taskId": "6e7c3783-3754-4636-a01a-3376c8d9a76d"}], "createdAt": "2025-06-03T06:57:17.195Z", "updatedAt": "2025-06-05T12:51:42.248Z", "relatedFiles": [{"path": "src/commands/handler.rs", "type": "CREATE", "description": "Command handler trait and processor"}, {"path": "src/commands/config_handler.rs", "type": "CREATE", "description": "Configuration command implementation"}, {"path": "src/commands/mcp_handler.rs", "type": "CREATE", "description": "MCP command implementation"}, {"path": "src/commands/doctor_handler.rs", "type": "CREATE", "description": "Doctor command implementation"}, {"path": "src/main.rs", "type": "TO_MODIFY", "description": "Replace TODO implementations", "lineStart": 180, "lineEnd": 310}], "implementationGuide": "1. Create src/commands/handler.rs with <PERSON><PERSON><PERSON><PERSON> trait\\n2. Implement concrete handlers: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>\\n3. Replace TODO implementations in main.rs with proper handler calls\\n4. Add command validation and error handling\\n5. Structure for easy extension of new commands\\n\\nHandler pattern:\\n```rust\\n#[async_trait]\\npub trait CommandHandler {\\n    async fn handle(&self, command: &Commands) -> Result<CommandResponse>;\\n    fn supports(&self, command: &Commands) -> bool;\\n}\\n\\npub struct CommandProcessor {\\n    handlers: Vec<Box<dyn CommandHandler>>,\\n}\\n\\nimpl CommandProcessor {\\n    pub async fn process(&self, command: &Commands) -> Result<CommandResponse> {\\n        // Route to appropriate handler\\n    }\\n}\\n```", "verificationCriteria": "All CLI subcommands work correctly, TODOs eliminated, command processing is extensible, error handling is consistent, and new commands can be added without modifying main.rs.", "analysisResult": "Comprehensive refactoring plan to transform AutoRun CLI from organic growth to maintainable architecture. Eliminates hard-coded values, separates UI from core logic, establishes proper provider abstractions, and implements missing features while maintaining backward compatibility. The refactoring addresses scattered provider matching logic, massive app.rs file mixing concerns, numerous TODO items, and configuration chaos through systematic architectural patterns.", "summary": "Successfully implemented Command Handler Pattern that eliminates all TODO implementations in main.rs and establishes extensible command processing. Created structured handlers for config, MCP, doctor, update, migrate, and ripgrep commands with consistent error handling and proper routing. All CLI subcommands now work correctly through the new pattern, making it easy to add new commands without modifying main.rs.", "completedAt": "2025-06-05T12:51:42.248Z"}, {"id": "9c61bc67-87a0-48d7-bddd-74cf8e742d9c", "name": "Create Service Container Pattern", "description": "Implement dependency injection pattern to replace scattered service creation and improve testability. Centralize service lifecycle management and eliminate tight coupling between components.", "notes": "Establishes proper dependency injection, improves testability by allowing mock services, and centralizes service management. Foundation for future modularization.", "status": "completed", "dependencies": [{"taskId": "b07efa9b-77b4-4315-9392-0612c250522a"}, {"taskId": "6e7c3783-3754-4636-a01a-3376c8d9a76d"}, {"taskId": "d6f4dfcf-644a-44da-9034-ed8dd5527a68"}], "createdAt": "2025-06-03T06:57:17.195Z", "updatedAt": "2025-06-05T13:15:49.435Z", "relatedFiles": [{"path": "src/core/service_container.rs", "type": "CREATE", "description": "Service container implementation"}, {"path": "src/app.rs", "type": "TO_MODIFY", "description": "Update to use service container", "lineStart": 144, "lineEnd": 280}, {"path": "src/main.rs", "type": "TO_MODIFY", "description": "Initialize services through container", "lineStart": 312, "lineEnd": 400}, {"path": "src/core/mod.rs", "type": "TO_MODIFY", "description": "Export service container"}], "implementationGuide": "1. Create src/core/service_container.rs with ServiceContainer struct\\n2. Define service traits for major components: ConfigService, ProviderService\\n3. Implement container registration and resolution methods\\n4. Update App initialization to use container\\n5. Add service lifecycle management (startup, shutdown)\\n\\nContainer structure:\\n```rust\\npub struct ServiceContainer {\\n    provider_factory: Arc<ProviderFactory>,\\n    config_registry: Arc<ConfigRegistry>,\\n    tool_executor: Arc<ToolExecutor>,\\n    command_processor: Arc<CommandProcessor>,\\n}\\n\\nimpl ServiceContainer {\\n    pub async fn new() -> Result<Self> { /* initialization */ }\\n    pub fn get_provider_factory(&self) -> Arc<ProviderFactory> { /* ... */ }\\n    pub fn get_config_registry(&self) -> Arc<ConfigRegistry> { /* ... */ }\\n}\\n```", "verificationCriteria": "Services are properly initialized and injected, application startup works correctly, components are loosely coupled, testability improved, and service lifecycle is managed consistently.", "analysisResult": "Comprehensive refactoring plan to transform AutoRun CLI from organic growth to maintainable architecture. Eliminates hard-coded values, separates UI from core logic, establishes proper provider abstractions, and implements missing features while maintaining backward compatibility. The refactoring addresses scattered provider matching logic, massive app.rs file mixing concerns, numerous TODO items, and configuration chaos through systematic architectural patterns.", "summary": "Successfully implemented Service Container Pattern that centralizes service creation and lifecycle management. Created ServiceContainer in src/core/service_container.rs with dependency injection for ProviderFactory, ConfigRegistry, ToolExecutor, CommandProcessor, ToolRegistry, and McpClientManager. Updated main.rs and app.rs to use the service container, eliminating scattered service creation throughout the codebase. The implementation includes service validation, startup/shutdown lifecycle methods, and proper error handling. All application modes (CLI commands, non-interactive, and TUI) now use the centralized service container, improving testability and reducing coupling between components.", "completedAt": "2025-06-05T13:15:49.435Z"}, {"id": "174595cc-d75a-4bc9-9c90-d24960bfd296", "name": "Consolidate Agent Core Factory Logic", "description": "Unify the duplicated agent core creation logic scattered across app.rs and main.rs. Create a centralized factory that handles tool support detection and provider-specific initialization.", "notes": "Eliminates code duplication between interactive and non-interactive modes. Centralizes complex tool support detection logic for maintainability.", "status": "completed", "dependencies": [{"taskId": "b07efa9b-77b4-4315-9392-0612c250522a"}, {"taskId": "8ee1b654-7908-4c83-a958-ad9249ae5029"}], "createdAt": "2025-06-03T06:57:17.195Z", "updatedAt": "2025-06-05T13:05:44.205Z", "relatedFiles": [{"path": "src/agent/factory.rs", "type": "CREATE", "description": "Agent core factory implementation"}, {"path": "src/app.rs", "type": "TO_MODIFY", "description": "Remove duplicated agent creation logic", "lineStart": 68, "lineEnd": 140}, {"path": "src/main.rs", "type": "TO_MODIFY", "description": "Use factory for agent creation", "lineStart": 365, "lineEnd": 387}, {"path": "src/agent/mod.rs", "type": "TO_MODIFY", "description": "Export factory"}], "implementationGuide": "1. Create src/agent/factory.rs with AgentCoreFactory\\n2. Move create_agent_core_with_tool_support logic from app.rs\\n3. Simplify agent initialization in both interactive and non-interactive modes\\n4. Add proper error handling and fallback strategies\\n5. Integrate with provider factory for consistent initialization\\n\\nFactory interface:\\n```rust\\npub struct AgentCoreFactory {\\n    provider_factory: Arc<ProviderFactory>,\\n}\\n\\nimpl AgentCoreFactory {\\n    pub async fn create_agent_core(&self, \\n        config: &LLMConfig,\\n        tool_executor: Arc<ToolExecutor>,\\n        execution_context: ExecutionContext,\\n    ) -> Result<AgentCore> {\\n        // Unified creation logic with tool support detection\\n    }\\n}\\n```", "verificationCriteria": "Agent core creation is consistent across all modes, tool support detection works correctly, provider-specific initialization is unified, error handling is improved, and code duplication is eliminated.", "analysisResult": "Comprehensive refactoring plan to transform AutoRun CLI from organic growth to maintainable architecture. Eliminates hard-coded values, separates UI from core logic, establishes proper provider abstractions, and implements missing features while maintaining backward compatibility. The refactoring addresses scattered provider matching logic, massive app.rs file mixing concerns, numerous TODO items, and configuration chaos through systematic architectural patterns.", "summary": "Successfully implemented AgentCoreFactory to consolidate duplicated agent creation logic across app.rs, main.rs, and app_core.rs. The factory centralizes tool support detection and provider-specific initialization, eliminating code duplication and improving maintainability. All three locations now use the same factory for consistent agent core creation with proper fallback strategies and error handling.", "completedAt": "2025-06-05T13:05:44.204Z"}]}