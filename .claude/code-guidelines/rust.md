# Idiomatic Rust: Mental Models and Best Practices

Beyond style guidelines, writing idiomatic Rust requires adopting specific mental models and approaches that work *with* the language rather than against it. Here are the key mental frameworks and practices that will help you write more idiomatic Rust code.

## **Embrace Ownership and Borrowing**

### Think in Terms of Resource Management
The most fundamental mental shift is understanding that Rust's ownership system is about **resource management**, not just memory safety. Instead of thinking "how do I share this data," think "who is responsible for this resource and when?"

- **Single ownership**: Each value has exactly one owner at any time
- **Borrowing for access**: Use references (`&T`) when you need temporary access without taking ownership
- **Mutable borrowing**: Only one mutable reference (`&mut T`) can exist at a time to prevent data races

### Mental Model for Borrowing
Think of borrowing like lending a book - you give temporary access while retaining ownership. The borrow checker ensures the "book" isn't destroyed while someone is still reading it.

## **Master Iterator-Driven Programming**

### Embrace Functional Programming Patterns
Rust's iterator model is one of the language's superpowers. Once you start thinking in iterators, manual loops become clunky and error-prone.

**Idiomatic iterator patterns:**
- Use `iter()` for immutable references to elements
- Use `iter_mut()` for mutable references when you need to modify elements
- Use `into_iter()` when you want to consume and take ownership
- Chain iterator adaptors like `map()`, `filter()`, and `collect()` for data transformation

```rust
// Instead of manual loops, use iterator chains
let doubled: Vec = numbers
    .iter()
    .filter(|&x| x % 2 == 0)
    .map(|x| x * 2)
    .collect();
```

## **Idiomatic Error Handling**

### Use Result and Option Types
Rust's approach to error handling centers on making failure explicit through types. The mental model is: "errors are values, not exceptions."

**Key practices:**
- Use `Result` for operations that can fail
- Use `Option` for values that might be absent
- Use the `?` operator to propagate errors concisely
- Avoid `unwrap()` and `expect()` in production code unless you're certain of success

## **Leverage the Type System**

### Think in Types, Not Just Data
Rust's type system is your ally for modeling domain concepts accurately. Use it to encode invariants and make invalid states unrepresentable.

**Idiomatic type usage:**
- Use `struct` types to group related data with meaningful names
- Use `enum` types to model exclusive alternatives and state machines
- Implement traits to define shared behavior across types
- Use generics with trait bounds to write flexible, reusable code

## **Work with Traits Idiomatically**

### Understand Common Trait Patterns
Traits are Rust's way of defining shared behavior. Think of them as contracts that types can implement.

**Key idiomatic patterns:**
- Implement `Display` and `Debug` for user-facing and developer-facing string representations
- Use `From` and `Into` for type conversions
- Implement `Iterator` for types that can be iterated over
- Use `Borrow` when you want to accept both owned and borrowed values

## **Pattern Matching as a Core Tool**

### Think Exhaustively
Pattern matching with `match` is not just for enums - it's a fundamental control flow mechanism. The mental model is: "handle all possibilities explicitly."

**Idiomatic matching:**
- Use `match` instead of cascading `if-else` statements when dealing with enums
- Leverage destructuring in patterns to extract data
- Use `if let` for simple single-pattern matches
- Always handle all cases or use `_` for catch-all patterns

## **Memory Management Mindset**

### Zero-Cost Abstractions
Think of Rust as providing high-level abstractions that compile down to efficient machine code. The mental model is: "safety without sacrifice."

**Key principles:**
- Stack allocation is preferred over heap allocation when possible
- Use `Box` for heap allocation when you need owned data of unknown size
- Use `Rc` and `Arc` sparingly, only when you truly need shared ownership
- Prefer borrowing over cloning for temporary access

## **Composition Over Inheritance**

### Favor Composition and Traits
Unlike object-oriented languages, Rust doesn't have inheritance. Instead, think in terms of composing behavior through traits and struct composition.

**Idiomatic patterns:**
- Use trait objects (`Box`) for runtime polymorphism
- Compose functionality by embedding structs within other structs
- Use associated types in traits for type-level relationships
- Implement multiple traits on the same type to provide different interfaces

## **Avoid Common Anti-Patterns**

### Don't Fight the Borrow Checker
When you encounter borrow checker issues, it's usually a sign that your design needs adjustment. Common solutions include:

- Restructuring code to avoid simultaneous mutable and immutable borrows  
- Using interior mutability patterns like `RefCell` when needed
- Considering whether you actually need shared mutable state
- Breaking up large functions into smaller, focused ones

### Lazy man error handling
Use built-in linters for proper error handling

```
#![deny(clippy::unwrap_used)]
#![deny(clippy::expect_used)]
#![deny(clippy::panic)]
#![deny(unused_must_use)]
```

### Neglecting STD traits

### Clone() everywhere
1. cloning struct fields
2. cloning in constructors
3. cloning shared data. Use Arc, RwLock for working with thread based manipulation

### Underutilizing pattern matching

### Glob imports/wildcard imports
- for third party library always explicitly import symbols
- use wildcard can cause issues when third party packages are changing dependencies