{"mcpServers": {"toolbox": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "@smithery/toolbox", "--key", "28a3eb18-6aa2-4371-b57d-7c9f9b3f6f30", "--profile", "defeated-wildcat-AFD7CG"]}, "shrimp-task-manager": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "@cjo4m06/mcp-shrimp-task-manager", "--key", "28a3eb18-6aa2-4371-b57d-7c9f9b3f6f30", "--profile", "defeated-wildcat-AFD7CG"], "env": {"DATA_DIR": "/Users/<USER>/Documents/TheAILeverage/Code/wisecode/autorun-rs/.claude/shrimp-data", "TEMPLATE_USE": "en", "ENABLE_GUI": "false"}}, "context7-mcp": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "@upstash/context7-mcp", "--key", "28a3eb18-6aa2-4371-b57d-7c9f9b3f6f30"]}, "clear-thought": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "@waldzellai/clear-thought", "--key", "28a3eb18-6aa2-4371-b57d-7c9f9b3f6f30"]}, "rmcp-docs": {"command": "npx", "args": ["mcp-remote", "https://gitmcp.io/modelcontextprotocol/rust-sdk/tree/main"]}, "playwright-mcp": {"command": "npx", "args": ["@playwright/mcp@latest", "--headless"]}, "repomix-mcp": {"command": "npx", "args": ["-y", "repomix", "--mcp"]}}}