// Enhanced TUI interface implementation
// Main interface that integrates all enhanced TUI components

use super::{
    Action, Mode, FocusTarget, TuiState, ContentNavigator, ReplInterface, 
    ComponentRegistry, ConfigManager, EnhancedTuiError, PerformanceMetrics
};
use crate::core::AppCore;
use crate::errors::Result;
use crossterm::event::{Event, KeyEvent, MouseEvent};
use ratatui::prelude::*;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::{mpsc, Mutex};
use uuid::Uuid;

/// Enhanced TUI interface that provides modern coding-focused experience
pub struct EnhancedTuiInterface {
    /// Current TUI state
    state: TuiState,
    
    /// Content navigator for managing content blocks
    content_navigator: ContentNavigator,
    
    /// REPL interface for command input
    repl_interface: ReplInterface,
    
    /// Component registry for UI components
    component_registry: ComponentRegistry,
    
    /// Configuration manager
    config_manager: Arc<Mutex<ConfigManager>>,
    
    /// Connection to app core
    app_core: Arc<Mutex<AppCore>>,
    
    /// Message channels
    message_tx: mpsc::Sender<crate::core::AppCoreMessage>,
    message_rx: mpsc::Receiver<crate::core::AppCoreMessage>,
    
    /// Performance monitoring
    performance_metrics: PerformanceMetrics,
    
    /// Last render time
    last_render: Instant,
    
    /// Frame counter
    frame_count: u64,
    
    /// Event processing times
    event_times: Vec<Duration>,
}

impl EnhancedTuiInterface {
    /// Create a new enhanced TUI interface
    pub async fn new(
        config: super::config::TuiConfig,
        vim_mode: bool,
        accessibility: bool,
        debug_mode: bool,
    ) -> Result<Self> {
        // Initialize configuration manager
        let config_dir = super::config::utils::get_default_config_dir()?;
        super::config::utils::ensure_config_dir(&config_dir).await?;
        let config_manager = Arc::new(Mutex::new(ConfigManager::new(config_dir).await?));
        
        // Initialize content navigator
        let mut content_navigator = ContentNavigator::new();
        
        // Initialize REPL interface
        let repl_config = super::input::repl::ReplConfig {
            vim_mode,
            auto_completion: config.ui.auto_completion,
            syntax_highlighting: config.ui.syntax_highlighting,
            multiline_input: true,
            history_size: config.performance.max_history_entries,
            auto_save_history: true,
            prompt_format: "❯ ".to_string(),
            tab_size: config.ui.tab_size as usize,
            auto_indent: true,
            show_line_numbers: config.ui.show_line_numbers,
            execution_timeout: 30,
        };
        let repl_interface = ReplInterface::new(repl_config);
        
        // Initialize component registry
        let component_factory = Arc::new(super::components::ComponentFactory::new());
        let component_registry = ComponentRegistry::new(component_factory);
        
        // Create message channels
        let (message_tx, message_rx) = mpsc::channel(1000);
        
        // Initialize state
        let mut state = TuiState::new();
        state.config.theme = config.theme;
        state.config.keybindings = config.keybindings;
        state.config.preferences.vim_mode = vim_mode;
        state.config.preferences.screen_reader = accessibility;
        state.debug.overlay_visible = debug_mode;
        
        // Create placeholder app core (will be set later)
        let app_core = Arc::new(Mutex::new(
            AppCore::new(
                crate::config::Config::default(),
                None,
                Arc::new(crate::tools::ToolRegistry::new()),
                Arc::new(tokio::sync::RwLock::new(crate::mcp::client::McpClientManager::new())),
            ).await?
        ));
        
        Ok(Self {
            state,
            content_navigator,
            repl_interface,
            component_registry,
            config_manager,
            app_core,
            message_tx,
            message_rx,
            performance_metrics: PerformanceMetrics::default(),
            last_render: Instant::now(),
            frame_count: 0,
            event_times: Vec::new(),
        })
    }
    
    /// Set the app core reference
    pub fn set_app_core(&mut self, app_core: Arc<Mutex<AppCore>>) {
        self.app_core = app_core;
    }
    
    /// Handle input events
    pub async fn handle_event(&mut self, event: Event) -> Result<Option<Action>> {
        let start_time = Instant::now();
        
        // Update performance metrics
        self.performance_metrics.event_processing_time = start_time.elapsed();
        
        let result = match event {
            Event::Key(key_event) => self.handle_key_event(key_event).await,
            Event::Mouse(mouse_event) => self.handle_mouse_event(mouse_event).await,
            Event::Resize(width, height) => self.handle_resize(width, height).await,
            _ => Ok(None),
        };
        
        // Record event processing time
        let processing_time = start_time.elapsed();
        self.event_times.push(processing_time);
        
        // Keep only recent event times for averaging
        if self.event_times.len() > 100 {
            self.event_times.remove(0);
        }
        
        // Update debug information
        if self.state.debug.overlay_visible {
            self.state.add_debug_event(
                "event_processed".to_string(),
                format!("Event processed in {:?}", processing_time),
                Some(format!("{:?}", event)),
            );
        }
        
        result
    }
    
    /// Handle keyboard events
    async fn handle_key_event(&mut self, key: KeyEvent) -> Result<Option<Action>> {
        // Handle global keybindings first
        if let Some(action) = self.handle_global_keybindings(&key).await? {
            return Ok(Some(action));
        }
        
        // Handle mode-specific input
        match self.state.mode {
            Mode::Normal => self.handle_normal_mode_key(key).await,
            Mode::Insert => self.handle_insert_mode_key(key).await,
            Mode::Visual => self.handle_visual_mode_key(key).await,
            Mode::Command => self.handle_command_mode_key(key).await,
            Mode::Widget => self.handle_widget_mode_key(key).await,
        }
    }
    
    /// Handle global keybindings
    async fn handle_global_keybindings(&mut self, key: &KeyEvent) -> Result<Option<Action>> {
        use crossterm::event::{KeyCode, KeyModifiers};
        
        match (key.code, key.modifiers) {
            // Exit application
            (KeyCode::Char('c'), KeyModifiers::CONTROL) => {
                Ok(Some(Action::System(super::SystemAction::Exit)))
            }
            
            // Toggle debug overlay
            (KeyCode::F12, _) => {
                self.state.debug.overlay_visible = !self.state.debug.overlay_visible;
                Ok(None)
            }
            
            // Reload configuration
            (KeyCode::F5, _) => {
                let mut config_manager = self.config_manager.lock().await;
                config_manager.check_for_changes().await?;
                Ok(Some(Action::System(super::SystemAction::ReloadConfig)))
            }
            
            _ => Ok(None),
        }
    }
    
    /// Handle normal mode keys
    async fn handle_normal_mode_key(&mut self, key: KeyEvent) -> Result<Option<Action>> {
        use crossterm::event::KeyCode;
        
        match key.code {
            KeyCode::Char('i') => {
                self.state.mode = Mode::Insert;
                self.state.focus = FocusTarget::InputArea;
                Ok(None)
            }
            KeyCode::Char('v') => {
                self.state.mode = Mode::Visual;
                Ok(None)
            }
            KeyCode::Char(':') => {
                self.state.mode = Mode::Command;
                self.state.focus = FocusTarget::InputArea;
                Ok(None)
            }
            KeyCode::Char('j') | KeyCode::Down => {
                let result = self.content_navigator.execute_command(
                    super::content_blocks::NavigationCommand::NextBlock
                );
                self.handle_navigation_result(result).await
            }
            KeyCode::Char('k') | KeyCode::Up => {
                let result = self.content_navigator.execute_command(
                    super::content_blocks::NavigationCommand::PreviousBlock
                );
                self.handle_navigation_result(result).await
            }
            KeyCode::Char('g') => {
                let result = self.content_navigator.execute_command(
                    super::content_blocks::NavigationCommand::FirstBlock
                );
                self.handle_navigation_result(result).await
            }
            KeyCode::Char('G') => {
                let result = self.content_navigator.execute_command(
                    super::content_blocks::NavigationCommand::LastBlock
                );
                self.handle_navigation_result(result).await
            }
            KeyCode::Char(' ') => {
                let result = self.content_navigator.execute_command(
                    super::content_blocks::NavigationCommand::ToggleSelection
                );
                self.handle_navigation_result(result).await
            }
            KeyCode::Char('/') => {
                self.state.mode = Mode::Command;
                self.state.focus = FocusTarget::InputArea;
                // TODO: Start search mode
                Ok(None)
            }
            _ => Ok(None),
        }
    }
    
    /// Handle insert mode keys
    async fn handle_insert_mode_key(&mut self, key: KeyEvent) -> Result<Option<Action>> {
        use crossterm::event::{KeyCode, KeyModifiers};
        
        match (key.code, key.modifiers) {
            (KeyCode::Esc, _) => {
                self.state.mode = Mode::Normal;
                self.state.focus = FocusTarget::ContentArea;
                Ok(None)
            }
            (KeyCode::Enter, KeyModifiers::NONE) => {
                // Submit input
                if let Ok(input) = self.repl_interface.submit() {
                    self.process_user_input(input).await?;
                }
                Ok(None)
            }
            (KeyCode::Enter, KeyModifiers::SHIFT) => {
                // New line in multi-line mode
                // TODO: Handle multi-line input
                Ok(None)
            }
            _ => {
                // Pass to REPL interface
                let event = Event::Key(key);
                match self.repl_interface.handle_event(&event)? {
                    super::input::InputResult::Action(action) => Ok(Some(action)),
                    _ => Ok(None),
                }
            }
        }
    }
    
    /// Handle visual mode keys
    async fn handle_visual_mode_key(&mut self, _key: KeyEvent) -> Result<Option<Action>> {
        // TODO: Implement visual mode
        Ok(None)
    }
    
    /// Handle command mode keys
    async fn handle_command_mode_key(&mut self, _key: KeyEvent) -> Result<Option<Action>> {
        // TODO: Implement command mode
        Ok(None)
    }
    
    /// Handle widget mode keys
    async fn handle_widget_mode_key(&mut self, key: KeyEvent) -> Result<Option<Action>> {
        use crossterm::event::KeyCode;
        
        match key.code {
            KeyCode::Esc => {
                self.state.mode = Mode::Normal;
                self.state.focus = FocusTarget::ContentArea;
                Ok(None)
            }
            KeyCode::Tab => {
                // Cycle through widgets
                // TODO: Implement widget cycling
                Ok(None)
            }
            _ => {
                // Pass to component registry
                let event = Event::Key(key);
                match self.component_registry.handle_event_for_focused(&event) {
                    super::ComponentResult::Action(action) => Ok(action),
                    _ => Ok(None),
                }
            }
        }
    }
    
    /// Handle mouse events
    async fn handle_mouse_event(&mut self, _mouse: MouseEvent) -> Result<Option<Action>> {
        // TODO: Implement mouse handling
        Ok(None)
    }
    
    /// Handle resize events
    async fn handle_resize(&mut self, width: u16, height: u16) -> Result<Option<Action>> {
        self.state.navigation.viewport_height = height.saturating_sub(6); // Account for UI elements
        self.content_navigator.set_viewport_height(self.state.navigation.viewport_height);
        Ok(None)
    }
    
    /// Handle navigation result
    async fn handle_navigation_result(
        &mut self, 
        result: super::content_blocks::NavigationResult
    ) -> Result<Option<Action>> {
        match result {
            super::content_blocks::NavigationResult::Success => Ok(None),
            super::content_blocks::NavigationResult::SuccessWithAction(action) => Ok(Some(action)),
            super::content_blocks::NavigationResult::SearchCompleted { results, current } => {
                self.state.navigation.search.results = vec![]; // TODO: Update with actual results
                Ok(None)
            }
            _ => Ok(None),
        }
    }
    
    /// Process user input
    async fn process_user_input(&mut self, input: String) -> Result<()> {
        // Add user message to content
        let user_block = super::content_blocks::ContentBlock::user_message(input.clone());
        self.content_navigator.add_block(user_block);
        
        // Send to app core
        let message = crate::core::AppCoreMessage::UserInput(input);
        self.message_tx.send(message).await?;
        
        // Update execution status
        self.state.execution = super::state::ExecutionStatus::Processing {
            message: "Processing user input...".to_string(),
            progress: None,
            started_at: chrono::Utc::now(),
        };
        
        Ok(())
    }
    
    /// Render the interface
    pub async fn render(&mut self, frame: &mut Frame) -> Result<()> {
        let start_time = Instant::now();
        
        // Update performance metrics
        self.performance_metrics.frame_time = self.last_render.elapsed();
        self.last_render = Instant::now();
        self.frame_count += 1;
        
        // Main layout
        let main_chunks = Layout::default()
            .direction(Direction::Vertical)
            .constraints([
                Constraint::Min(10),      // Content area
                Constraint::Length(5),    // Input area
                Constraint::Length(1),    // Status bar
            ])
            .split(frame.area());
        
        // Render content area
        self.render_content_area(frame, main_chunks[0]).await?;
        
        // Render input area
        self.render_input_area(frame, main_chunks[1]).await?;
        
        // Render status bar
        self.render_status_bar(frame, main_chunks[2]).await?;
        
        // Render debug overlay if enabled
        if self.state.debug.overlay_visible {
            self.render_debug_overlay(frame).await?;
        }
        
        // Update render time
        self.performance_metrics.render_time = start_time.elapsed();
        
        Ok(())
    }
    
    /// Render content area
    async fn render_content_area(&mut self, frame: &mut Frame, area: Rect) -> Result<()> {
        let visible_blocks = self.content_navigator.get_visible_blocks();
        
        if visible_blocks.is_empty() {
            // Show welcome message
            let welcome = Paragraph::new("Welcome to AutoRun Enhanced TUI\n\nType 'i' to enter insert mode and start chatting!")
                .style(Style::default().fg(Color::Gray))
                .alignment(Alignment::Center)
                .block(Block::default()
                    .borders(Borders::ALL)
                    .title("Content")
                    .border_style(Style::default().fg(Color::Blue)));
            
            frame.render_widget(welcome, area);
            return Ok(());
        }
        
        // Render content blocks
        let block_height = 3; // Minimum height per block
        let visible_count = (area.height.saturating_sub(2) / block_height) as usize;
        let start_index = self.state.navigation.scroll_offset;
        let end_index = (start_index + visible_count).min(visible_blocks.len());
        
        let block_areas = Layout::default()
            .direction(Direction::Vertical)
            .constraints(
                (start_index..end_index)
                    .map(|_| Constraint::Length(block_height))
                    .collect::<Vec<_>>()
            )
            .split(area);
        
        for (i, block) in visible_blocks[start_index..end_index].iter().enumerate() {
            if i < block_areas.len() {
                self.render_content_block(frame, block_areas[i], block).await?;
            }
        }
        
        Ok(())
    }
    
    /// Render individual content block
    async fn render_content_block(
        &self, 
        frame: &mut Frame, 
        area: Rect, 
        block: &super::content_blocks::ContentBlock
    ) -> Result<()> {
        let style = block.get_border_style();
        let title = format!("{} - {}", block.get_type_name(), 
            block.timestamp.format("%H:%M:%S"));
        
        let content = Paragraph::new(block.content.as_str())
            .style(Style::default())
            .wrap(Wrap { trim: true })
            .block(Block::default()
                .borders(Borders::ALL)
                .title(title)
                .border_style(style));
        
        frame.render_widget(content, area);
        Ok(())
    }
    
    /// Render input area
    async fn render_input_area(&mut self, frame: &mut Frame, area: Rect) -> Result<()> {
        let repl_state = self.repl_interface.get_state();
        let border_style = match self.state.focus {
            FocusTarget::InputArea => Style::default().fg(Color::Cyan),
            _ => Style::default().fg(Color::Gray),
        };
        
        let input_widget = Paragraph::new(repl_state.input.as_str())
            .style(Style::default())
            .block(Block::default()
                .borders(Borders::ALL)
                .title(format!("{} [{}]", repl_state.prompt, self.state.mode.to_string()))
                .border_style(border_style));
        
        frame.render_widget(input_widget, area);
        Ok(())
    }
    
    /// Render status bar
    async fn render_status_bar(&mut self, frame: &mut Frame, area: Rect) -> Result<()> {
        let status_text = match &self.state.execution {
            super::state::ExecutionStatus::Idle => "Ready".to_string(),
            super::state::ExecutionStatus::Processing { message, .. } => message.clone(),
            super::state::ExecutionStatus::Completed { message, .. } => message.clone(),
            super::state::ExecutionStatus::Failed { error, .. } => format!("Error: {}", error),
        };
        
        let help_text = match self.state.mode {
            Mode::Normal => " j/k:navigate i:insert v:visual /:search ",
            Mode::Insert => " ESC:normal Enter:send Shift+Enter:newline ",
            Mode::Visual => " ESC:normal y:copy d:delete ",
            Mode::Command => " ESC:cancel Enter:execute ",
            Mode::Widget => " ESC:normal Tab:next-widget ",
        };
        
        let status_bar = Paragraph::new(Line::from(vec![
            Span::styled(format!(" {} ", status_text), Style::default().bg(Color::DarkGray)),
            Span::raw(" "),
            Span::styled(help_text, Style::default().fg(Color::DarkGray)),
        ]))
        .alignment(Alignment::Left);
        
        frame.render_widget(status_bar, area);
        Ok(())
    }
    
    /// Render debug overlay
    async fn render_debug_overlay(&mut self, frame: &mut Frame) -> Result<()> {
        let area = super::utils_ext::centered_rect(60, 40, frame.area());
        
        let avg_frame_time = if !self.event_times.is_empty() {
            self.event_times.iter().sum::<Duration>() / self.event_times.len() as u32
        } else {
            Duration::ZERO
        };
        
        let debug_text = format!(
            "Debug Information\n\n\
            Frame: {} | FPS: {:.1}\n\
            Frame Time: {:.2}ms\n\
            Render Time: {:.2}ms\n\
            Event Time: {:.2}ms\n\
            Content Blocks: {}\n\
            Memory Usage: {}KB\n\
            Mode: {:?}\n\
            Focus: {:?}",
            self.frame_count,
            1000.0 / self.performance_metrics.frame_time.as_millis().max(1) as f64,
            self.performance_metrics.frame_time.as_secs_f64() * 1000.0,
            self.performance_metrics.render_time.as_secs_f64() * 1000.0,
            avg_frame_time.as_secs_f64() * 1000.0,
            self.content_navigator.get_visible_blocks().len(),
            self.performance_metrics.memory_usage / 1024,
            self.state.mode,
            self.state.focus,
        );
        
        let debug_widget = Paragraph::new(debug_text)
            .style(Style::default().fg(Color::Yellow))
            .block(Block::default()
                .borders(Borders::ALL)
                .title("Debug Overlay (F12 to toggle)")
                .border_style(Style::default().fg(Color::Yellow)));
        
        frame.render_widget(Clear, area);
        frame.render_widget(debug_widget, area);
        
        Ok(())
    }
    
    /// Get current state
    pub fn get_state(&self) -> &TuiState {
        &self.state
    }
    
    /// Check if interface is ready for input
    pub fn is_ready(&self) -> bool {
        self.repl_interface.is_ready()
    }
    
    /// Process messages from app core
    pub async fn process_messages(&mut self) -> Result<()> {
        while let Ok(message) = self.message_rx.try_recv() {
            match message {
                crate::core::AppCoreMessage::AgentResponse(response) => {
                    let response_block = super::content_blocks::ContentBlock::assistant_response(response);
                    self.content_navigator.add_block(response_block);
                    
                    self.state.execution = super::state::ExecutionStatus::Completed {
                        message: "Response received".to_string(),
                        duration: Duration::from_millis(100), // TODO: Calculate actual duration
                        completed_at: chrono::Utc::now(),
                    };
                }
                crate::core::AppCoreMessage::Error(error) => {
                    let error_block = super::content_blocks::ContentBlock::error(
                        error,
                        super::content_blocks::ErrorType::Execution,
                        super::content_blocks::ErrorSeverity::Error,
                    );
                    self.content_navigator.add_block(error_block);
                    
                    self.state.execution = super::state::ExecutionStatus::Failed {
                        error: "Processing failed".to_string(),
                        failed_at: chrono::Utc::now(),
                    };
                }
                _ => {
                    // Handle other message types
                }
            }
        }
        
        Ok(())
    }
}

impl Mode {
    fn to_string(&self) -> &'static str {
        match self {
            Mode::Normal => "NORMAL",
            Mode::Insert => "INSERT",
            Mode::Visual => "VISUAL",
            Mode::Command => "COMMAND",
            Mode::Widget => "WIDGET",
        }
    }
}
