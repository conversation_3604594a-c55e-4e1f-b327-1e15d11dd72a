// Configuration system for enhanced TUI
// Provides theming, keybinding configuration, and hot-reloading capabilities

pub mod theme;
pub mod keybindings;
pub mod hot_reload;

pub use theme::{Theme, ColorScheme, StyleConfig, ThemeManager, SyntaxTheme};
pub use keybindings::{KeybindingConfig, KeybindingManager, KeyBinding, ActionMapping};
pub use hot_reload::{ConfigWatcher, ConfigEvent, HotReloadManager};

use crate::errors::Result;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::{Path, PathBuf};
use tokio::sync::RwLock;
use std::sync::Arc;

/// Main TUI configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TuiConfig {
    /// Theme configuration
    pub theme: Theme,
    
    /// Keybinding configuration
    pub keybindings: KeybindingConfig,
    
    /// UI preferences
    pub ui: UiConfig,
    
    /// Feature flags
    pub features: FeatureConfig,
    
    /// Performance settings
    pub performance: PerformanceConfig,
    
    /// Accessibility settings
    pub accessibility: AccessibilityConfig,
}

/// UI configuration
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct UiConfig {
    /// Enable vim keybindings
    pub vim_mode: bool,
    
    /// Enable auto-completion
    pub auto_completion: bool,
    
    /// Enable syntax highlighting
    pub syntax_highlighting: bool,
    
    /// Enable animations
    pub animations: bool,
    
    /// Show line numbers
    pub show_line_numbers: bool,
    
    /// Word wrap
    pub word_wrap: bool,
    
    /// Tab size
    pub tab_size: u8,
    
    /// Font size (for terminals that support it)
    pub font_size: u16,
    
    /// Cursor style
    pub cursor_style: CursorStyle,
    
    /// Scroll behavior
    pub scroll_behavior: ScrollBehavior,
}

/// Feature configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FeatureConfig {
    /// Enable enhanced content blocks
    pub enhanced_blocks: bool,
    
    /// Enable advanced completion
    pub advanced_completion: bool,
    
    /// Enable virtual scrolling
    pub virtual_scrolling: bool,
    
    /// Enable hot reloading
    pub hot_reload: bool,
    
    /// Enable debug mode
    pub debug_mode: bool,
    
    /// Enable performance monitoring
    pub performance_monitoring: bool,
    
    /// Enable experimental features
    pub experimental: bool,
}

/// Performance configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceConfig {
    /// Maximum content blocks to keep in memory
    pub max_content_blocks: usize,
    
    /// Maximum history entries
    pub max_history_entries: usize,
    
    /// Render frame rate limit (FPS)
    pub max_fps: u16,
    
    /// Enable lazy rendering
    pub lazy_rendering: bool,
    
    /// Cache size for various caches
    pub cache_size: usize,
    
    /// Garbage collection interval (seconds)
    pub gc_interval: u64,
}

/// Accessibility configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AccessibilityConfig {
    /// Enable screen reader support
    pub screen_reader: bool,
    
    /// High contrast mode
    pub high_contrast: bool,
    
    /// Reduce motion
    pub reduce_motion: bool,
    
    /// Large text mode
    pub large_text: bool,
    
    /// Announcement level
    pub announcement_level: AnnouncementLevel,
    
    /// Focus indicators
    pub focus_indicators: bool,
    
    /// Sound feedback
    pub sound_feedback: bool,
}

/// Cursor style options
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum CursorStyle {
    Block,
    Underline,
    Bar,
}

/// Scroll behavior options
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ScrollBehavior {
    Smooth,
    Instant,
    Page,
}

/// Announcement level for screen readers
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AnnouncementLevel {
    Minimal,
    Normal,
    Verbose,
}

/// Configuration manager
pub struct ConfigManager {
    /// Current configuration
    config: Arc<RwLock<TuiConfig>>,
    
    /// Configuration file path
    config_path: PathBuf,
    
    /// Theme manager
    theme_manager: ThemeManager,
    
    /// Keybinding manager
    keybinding_manager: KeybindingManager,
    
    /// Hot reload manager
    hot_reload_manager: Option<HotReloadManager>,
    
    /// Configuration watchers
    watchers: Vec<ConfigWatcher>,
}

impl ConfigManager {
    /// Create a new configuration manager
    pub async fn new(config_dir: PathBuf) -> Result<Self> {
        let config_path = config_dir.join("config.toml");
        
        // Load or create default configuration
        let config = if config_path.exists() {
            Self::load_config(&config_path).await?
        } else {
            let default_config = TuiConfig::default();
            Self::save_config(&config_path, &default_config).await?;
            default_config
        };
        
        let theme_manager = ThemeManager::new(config_dir.join("themes")).await?;
        let keybinding_manager = KeybindingManager::new(config_dir.join("keybindings")).await?;
        
        let hot_reload_manager = if config.features.hot_reload {
            Some(HotReloadManager::new(config_dir.clone()).await?)
        } else {
            None
        };
        
        Ok(Self {
            config: Arc::new(RwLock::new(config)),
            config_path,
            theme_manager,
            keybinding_manager,
            hot_reload_manager,
            watchers: Vec::new(),
        })
    }
    
    /// Get current configuration
    pub async fn get_config(&self) -> TuiConfig {
        self.config.read().await.clone()
    }
    
    /// Update configuration
    pub async fn update_config<F>(&self, updater: F) -> Result<()>
    where
        F: FnOnce(&mut TuiConfig),
    {
        let mut config = self.config.write().await;
        updater(&mut config);
        
        // Save updated configuration
        Self::save_config(&self.config_path, &config).await?;
        
        Ok(())
    }
    
    /// Get theme manager
    pub fn get_theme_manager(&self) -> &ThemeManager {
        &self.theme_manager
    }
    
    /// Get keybinding manager
    pub fn get_keybinding_manager(&self) -> &KeybindingManager {
        &self.keybinding_manager
    }
    
    /// Start hot reloading
    pub async fn start_hot_reload(&mut self) -> Result<()> {
        if let Some(hot_reload) = &mut self.hot_reload_manager {
            hot_reload.start().await?;
        }
        Ok(())
    }
    
    /// Stop hot reloading
    pub async fn stop_hot_reload(&mut self) -> Result<()> {
        if let Some(hot_reload) = &mut self.hot_reload_manager {
            hot_reload.stop().await?;
        }
        Ok(())
    }
    
    /// Check for configuration changes
    pub async fn check_for_changes(&mut self) -> Result<Vec<ConfigEvent>> {
        let mut events = Vec::new();
        
        if let Some(hot_reload) = &mut self.hot_reload_manager {
            events.extend(hot_reload.get_events().await?);
        }
        
        // Process events
        for event in &events {
            self.handle_config_event(event).await?;
        }
        
        Ok(events)
    }
    
    /// Handle configuration event
    async fn handle_config_event(&mut self, event: &ConfigEvent) -> Result<()> {
        match event {
            ConfigEvent::ConfigChanged => {
                // Reload main configuration
                let new_config = Self::load_config(&self.config_path).await?;
                *self.config.write().await = new_config;
            }
            ConfigEvent::ThemeChanged(theme_name) => {
                // Reload specific theme
                if let Ok(theme) = self.theme_manager.load_theme(theme_name).await {
                    self.update_config(|config| {
                        config.theme = theme;
                    }).await?;
                }
            }
            ConfigEvent::KeybindingsChanged => {
                // Reload keybindings
                if let Ok(keybindings) = self.keybinding_manager.load_default_keybindings().await {
                    self.update_config(|config| {
                        config.keybindings = keybindings;
                    }).await?;
                }
            }
        }
        
        Ok(())
    }
    
    /// Load configuration from file
    async fn load_config(path: &Path) -> Result<TuiConfig> {
        let content = tokio::fs::read_to_string(path).await?;
        let config: TuiConfig = toml::from_str(&content)?;
        Ok(config)
    }
    
    /// Save configuration to file
    async fn save_config(path: &Path, config: &TuiConfig) -> Result<()> {
        // Ensure parent directory exists
        if let Some(parent) = path.parent() {
            tokio::fs::create_dir_all(parent).await?;
        }
        
        let content = toml::to_string_pretty(config)?;
        tokio::fs::write(path, content).await?;
        Ok(())
    }
    
    /// Reset configuration to defaults
    pub async fn reset_to_defaults(&self) -> Result<()> {
        let default_config = TuiConfig::default();
        *self.config.write().await = default_config.clone();
        Self::save_config(&self.config_path, &default_config).await?;
        Ok(())
    }
    
    /// Export configuration
    pub async fn export_config(&self, path: &Path) -> Result<()> {
        let config = self.config.read().await;
        Self::save_config(path, &config).await
    }
    
    /// Import configuration
    pub async fn import_config(&self, path: &Path) -> Result<()> {
        let config = Self::load_config(path).await?;
        *self.config.write().await = config.clone();
        Self::save_config(&self.config_path, &config).await?;
        Ok(())
    }
    
    /// Validate configuration
    pub async fn validate_config(&self) -> Result<Vec<String>> {
        let config = self.config.read().await;
        let mut errors = Vec::new();
        
        // Validate theme
        if let Err(e) = self.theme_manager.validate_theme(&config.theme).await {
            errors.push(format!("Theme validation error: {}", e));
        }
        
        // Validate keybindings
        if let Err(e) = self.keybinding_manager.validate_keybindings(&config.keybindings).await {
            errors.push(format!("Keybinding validation error: {}", e));
        }
        
        // Validate performance settings
        if config.performance.max_fps == 0 {
            errors.push("Max FPS cannot be zero".to_string());
        }
        
        if config.performance.max_content_blocks == 0 {
            errors.push("Max content blocks cannot be zero".to_string());
        }
        
        Ok(errors)
    }
}

impl Default for TuiConfig {
    fn default() -> Self {
        Self {
            theme: Theme::default(),
            keybindings: KeybindingConfig::default(),
            ui: UiConfig::default(),
            features: FeatureConfig::default(),
            performance: PerformanceConfig::default(),
            accessibility: AccessibilityConfig::default(),
        }
    }
}

impl Default for UiConfig {
    fn default() -> Self {
        Self {
            vim_mode: false,
            auto_completion: true,
            syntax_highlighting: true,
            animations: true,
            show_line_numbers: true,
            word_wrap: true,
            tab_size: 4,
            font_size: 14,
            cursor_style: CursorStyle::Block,
            scroll_behavior: ScrollBehavior::Smooth,
        }
    }
}

impl Default for FeatureConfig {
    fn default() -> Self {
        Self {
            enhanced_blocks: true,
            advanced_completion: true,
            virtual_scrolling: true,
            hot_reload: true,
            debug_mode: false,
            performance_monitoring: false,
            experimental: false,
        }
    }
}

impl Default for PerformanceConfig {
    fn default() -> Self {
        Self {
            max_content_blocks: 10000,
            max_history_entries: 1000,
            max_fps: 60,
            lazy_rendering: true,
            cache_size: 1000,
            gc_interval: 300, // 5 minutes
        }
    }
}

impl Default for AccessibilityConfig {
    fn default() -> Self {
        Self {
            screen_reader: false,
            high_contrast: false,
            reduce_motion: false,
            large_text: false,
            announcement_level: AnnouncementLevel::Normal,
            focus_indicators: true,
            sound_feedback: false,
        }
    }
}

/// Configuration utilities
pub mod utils {
    use super::*;
    
    /// Get default configuration directory
    pub fn get_default_config_dir() -> Result<PathBuf> {
        let home = dirs::home_dir()
            .ok_or_else(|| crate::errors::AutorunError::NotFound("Home directory not found".to_string()))?;
        
        Ok(home.join(".config").join("autorun"))
    }
    
    /// Ensure configuration directory exists
    pub async fn ensure_config_dir(path: &Path) -> Result<()> {
        if !path.exists() {
            tokio::fs::create_dir_all(path).await?;
        }
        Ok(())
    }
    
    /// Create default configuration files
    pub async fn create_default_configs(config_dir: &Path) -> Result<()> {
        ensure_config_dir(config_dir).await?;
        
        // Create main config
        let config_path = config_dir.join("config.toml");
        if !config_path.exists() {
            let default_config = TuiConfig::default();
            let content = toml::to_string_pretty(&default_config)?;
            tokio::fs::write(config_path, content).await?;
        }
        
        // Create themes directory
        let themes_dir = config_dir.join("themes");
        ensure_config_dir(&themes_dir).await?;
        
        // Create keybindings directory
        let keybindings_dir = config_dir.join("keybindings");
        ensure_config_dir(&keybindings_dir).await?;
        
        Ok(())
    }
    
    /// Backup configuration
    pub async fn backup_config(config_dir: &Path, backup_name: &str) -> Result<PathBuf> {
        let backup_dir = config_dir.join("backups");
        ensure_config_dir(&backup_dir).await?;
        
        let timestamp = chrono::Utc::now().format("%Y%m%d_%H%M%S");
        let backup_path = backup_dir.join(format!("{}_{}.tar.gz", backup_name, timestamp));
        
        // TODO: Implement actual backup creation (tar.gz)
        // For now, just copy the config file
        let config_path = config_dir.join("config.toml");
        let backup_config_path = backup_dir.join(format!("config_{}_{}.toml", backup_name, timestamp));
        
        if config_path.exists() {
            tokio::fs::copy(config_path, backup_config_path).await?;
        }
        
        Ok(backup_path)
    }
    
    /// Restore configuration from backup
    pub async fn restore_config(config_dir: &Path, backup_path: &Path) -> Result<()> {
        // TODO: Implement actual backup restoration
        // For now, just copy the backup config file
        if backup_path.exists() {
            let config_path = config_dir.join("config.toml");
            tokio::fs::copy(backup_path, config_path).await?;
        }
        
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;
    
    #[tokio::test]
    async fn test_config_manager_creation() {
        let temp_dir = TempDir::new().unwrap();
        let config_dir = temp_dir.path().to_path_buf();
        
        let manager = ConfigManager::new(config_dir).await.unwrap();
        let config = manager.get_config().await;
        
        assert_eq!(config.ui.vim_mode, false);
        assert_eq!(config.ui.auto_completion, true);
    }
    
    #[tokio::test]
    async fn test_config_serialization() {
        let config = TuiConfig::default();
        let serialized = toml::to_string(&config).unwrap();
        let deserialized: TuiConfig = toml::from_str(&serialized).unwrap();
        
        assert_eq!(config.ui.vim_mode, deserialized.ui.vim_mode);
        assert_eq!(config.features.enhanced_blocks, deserialized.features.enhanced_blocks);
    }
    
    #[tokio::test]
    async fn test_config_update() {
        let temp_dir = TempDir::new().unwrap();
        let config_dir = temp_dir.path().to_path_buf();
        
        let manager = ConfigManager::new(config_dir).await.unwrap();
        
        manager.update_config(|config| {
            config.ui.vim_mode = true;
            config.ui.font_size = 16;
        }).await.unwrap();
        
        let updated_config = manager.get_config().await;
        assert_eq!(updated_config.ui.vim_mode, true);
        assert_eq!(updated_config.ui.font_size, 16);
    }
}
