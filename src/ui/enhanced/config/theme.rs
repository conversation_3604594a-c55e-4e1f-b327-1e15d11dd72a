// Theme system for enhanced TUI
// Provides comprehensive theming with color schemes, styles, and syntax highlighting

use crate::errors::Result;
use ratatui::prelude::*;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::{Path, PathBuf};

/// Complete theme configuration
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Theme {
    /// Theme metadata
    pub metadata: ThemeMetadata,
    
    /// Color scheme
    pub colors: ColorScheme,
    
    /// Style configuration
    pub styles: StyleConfig,
    
    /// Syntax highlighting theme
    pub syntax: SyntaxTheme,
    
    /// Border configuration
    pub borders: BorderConfig,
    
    /// Component-specific styling
    pub components: ComponentStyles,
}

/// Theme metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ThemeMetadata {
    /// Theme name
    pub name: String,
    
    /// Theme description
    pub description: String,
    
    /// Theme author
    pub author: String,
    
    /// Theme version
    pub version: String,
    
    /// Base theme (for inheritance)
    pub base: Option<String>,
    
    /// Theme tags
    pub tags: Vec<String>,
    
    /// Dark or light theme
    pub variant: ThemeVariant,
}

/// Theme variant
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum ThemeVariant {
    Dark,
    Light,
    Auto, // Follows system preference
}

/// Color scheme configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ColorScheme {
    /// Primary colors
    pub primary: Color,
    pub secondary: Color,
    pub accent: Color,
    
    /// Background colors
    pub background: Color,
    pub surface: Color,
    pub overlay: Color,
    
    /// Foreground colors
    pub foreground: Color,
    pub muted: Color,
    pub subtle: Color,
    
    /// Semantic colors
    pub success: Color,
    pub warning: Color,
    pub error: Color,
    pub info: Color,
    
    /// UI element colors
    pub border: Color,
    pub selection: Color,
    pub highlight: Color,
    pub focus: Color,
    
    /// Content type colors
    pub user_message: Color,
    pub assistant_message: Color,
    pub code_block: Color,
    pub tool_output: Color,
    pub system_message: Color,
}

/// Style configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StyleConfig {
    /// Default text style
    pub default: StyleDefinition,
    
    /// Heading styles
    pub heading1: StyleDefinition,
    pub heading2: StyleDefinition,
    pub heading3: StyleDefinition,
    
    /// Emphasis styles
    pub bold: StyleDefinition,
    pub italic: StyleDefinition,
    pub underline: StyleDefinition,
    pub strikethrough: StyleDefinition,
    
    /// Code styles
    pub code_inline: StyleDefinition,
    pub code_block: StyleDefinition,
    
    /// Link style
    pub link: StyleDefinition,
    
    /// Quote style
    pub quote: StyleDefinition,
    
    /// List styles
    pub list_item: StyleDefinition,
    pub list_marker: StyleDefinition,
}

/// Style definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StyleDefinition {
    /// Foreground color
    pub fg: Option<Color>,
    
    /// Background color
    pub bg: Option<Color>,
    
    /// Text modifiers
    pub modifiers: Vec<ModifierType>,
}

/// Text modifier types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ModifierType {
    Bold,
    Italic,
    Underlined,
    SlowBlink,
    RapidBlink,
    Reversed,
    CrossedOut,
    Dim,
}

/// Syntax highlighting theme
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SyntaxTheme {
    /// Keywords (if, else, function, etc.)
    pub keyword: Color,
    
    /// String literals
    pub string: Color,
    
    /// Numeric literals
    pub number: Color,
    
    /// Comments
    pub comment: Color,
    
    /// Function names
    pub function: Color,
    
    /// Variable names
    pub variable: Color,
    
    /// Type names
    pub type_name: Color,
    
    /// Constants
    pub constant: Color,
    
    /// Operators
    pub operator: Color,
    
    /// Punctuation
    pub punctuation: Color,
    
    /// Preprocessor directives
    pub preprocessor: Color,
    
    /// Regular expressions
    pub regex: Color,
    
    /// Escape sequences
    pub escape: Color,
    
    /// Invalid/error syntax
    pub invalid: Color,
}

/// Border configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BorderConfig {
    /// Default border style
    pub default: BorderStyle,
    
    /// Focused element border
    pub focused: BorderStyle,
    
    /// Selected element border
    pub selected: BorderStyle,
    
    /// Error border
    pub error: BorderStyle,
    
    /// Warning border
    pub warning: BorderStyle,
    
    /// Success border
    pub success: BorderStyle,
}

/// Border style definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BorderStyle {
    /// Border type
    pub border_type: BorderType,
    
    /// Border color
    pub color: Color,
    
    /// Border thickness (for terminals that support it)
    pub thickness: u8,
}

/// Border type
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum BorderType {
    Plain,
    Rounded,
    Double,
    Thick,
    Custom {
        top: String,
        bottom: String,
        left: String,
        right: String,
        top_left: String,
        top_right: String,
        bottom_left: String,
        bottom_right: String,
    },
}

/// Component-specific styles
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComponentStyles {
    /// Status bar styling
    pub status_bar: ComponentStyle,
    
    /// Input area styling
    pub input_area: ComponentStyle,
    
    /// Content area styling
    pub content_area: ComponentStyle,
    
    /// Completion popup styling
    pub completion_popup: ComponentStyle,
    
    /// Dialog styling
    pub dialog: ComponentStyle,
    
    /// Progress bar styling
    pub progress_bar: ComponentStyle,
    
    /// Combobox styling
    pub combobox: ComponentStyle,
}

/// Individual component style
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComponentStyle {
    /// Normal state
    pub normal: StyleDefinition,
    
    /// Focused state
    pub focused: StyleDefinition,
    
    /// Selected state
    pub selected: StyleDefinition,
    
    /// Disabled state
    pub disabled: StyleDefinition,
    
    /// Hover state (for mouse interaction)
    pub hover: StyleDefinition,
    
    /// Active state (being pressed/clicked)
    pub active: StyleDefinition,
}

/// Theme manager for loading and managing themes
pub struct ThemeManager {
    /// Themes directory
    themes_dir: PathBuf,
    
    /// Loaded themes cache
    themes_cache: HashMap<String, Theme>,
    
    /// Current theme name
    current_theme: String,
}

impl ThemeManager {
    /// Create a new theme manager
    pub async fn new(themes_dir: PathBuf) -> Result<Self> {
        // Ensure themes directory exists
        if !themes_dir.exists() {
            tokio::fs::create_dir_all(&themes_dir).await?;
        }
        
        let mut manager = Self {
            themes_dir,
            themes_cache: HashMap::new(),
            current_theme: "default".to_string(),
        };
        
        // Create default themes if they don't exist
        manager.create_default_themes().await?;
        
        Ok(manager)
    }
    
    /// Load a theme by name
    pub async fn load_theme(&mut self, name: &str) -> Result<Theme> {
        // Check cache first
        if let Some(theme) = self.themes_cache.get(name) {
            return Ok(theme.clone());
        }
        
        // Load from file
        let theme_path = self.themes_dir.join(format!("{}.toml", name));
        if !theme_path.exists() {
            return Err(crate::errors::AutorunError::NotFound(
                format!("Theme '{}' not found", name)
            ).into());
        }
        
        let content = tokio::fs::read_to_string(&theme_path).await?;
        let mut theme: Theme = toml::from_str(&content)?;
        
        // Handle theme inheritance
        if let Some(base_name) = &theme.metadata.base {
            let base_theme = self.load_theme(base_name).await?;
            theme = self.merge_themes(base_theme, theme);
        }
        
        // Cache the theme
        self.themes_cache.insert(name.to_string(), theme.clone());
        
        Ok(theme)
    }
    
    /// Save a theme
    pub async fn save_theme(&self, theme: &Theme) -> Result<()> {
        let theme_path = self.themes_dir.join(format!("{}.toml", theme.metadata.name));
        let content = toml::to_string_pretty(theme)?;
        tokio::fs::write(theme_path, content).await?;
        Ok(())
    }
    
    /// List available themes
    pub async fn list_themes(&self) -> Result<Vec<String>> {
        let mut themes = Vec::new();
        
        let mut entries = tokio::fs::read_dir(&self.themes_dir).await?;
        while let Some(entry) = entries.next_entry().await? {
            if let Some(name) = entry.file_name().to_str() {
                if name.ends_with(".toml") {
                    let theme_name = name.trim_end_matches(".toml");
                    themes.push(theme_name.to_string());
                }
            }
        }
        
        themes.sort();
        Ok(themes)
    }
    
    /// Get current theme
    pub async fn get_current_theme(&mut self) -> Result<Theme> {
        self.load_theme(&self.current_theme.clone()).await
    }
    
    /// Set current theme
    pub fn set_current_theme(&mut self, name: String) {
        self.current_theme = name;
    }
    
    /// Validate theme
    pub async fn validate_theme(&self, theme: &Theme) -> Result<()> {
        // Basic validation
        if theme.metadata.name.is_empty() {
            return Err(crate::errors::AutorunError::InvalidInput(
                "Theme name cannot be empty".to_string()
            ).into());
        }
        
        // Validate color values
        // (In a real implementation, you might validate that colors are valid)
        
        Ok(())
    }
    
    /// Create default themes
    async fn create_default_themes(&self) -> Result<()> {
        // Create default theme
        let default_theme = Theme::default();
        let default_path = self.themes_dir.join("default.toml");
        if !default_path.exists() {
            self.save_theme(&default_theme).await?;
        }
        
        // Create dark theme
        let dark_theme = Theme::dark();
        let dark_path = self.themes_dir.join("dark.toml");
        if !dark_path.exists() {
            self.save_theme(&dark_theme).await?;
        }
        
        // Create light theme
        let light_theme = Theme::light();
        let light_path = self.themes_dir.join("light.toml");
        if !light_path.exists() {
            self.save_theme(&light_theme).await?;
        }
        
        // Create high contrast theme
        let high_contrast_theme = Theme::high_contrast();
        let high_contrast_path = self.themes_dir.join("high_contrast.toml");
        if !high_contrast_path.exists() {
            self.save_theme(&high_contrast_theme).await?;
        }
        
        Ok(())
    }
    
    /// Merge base theme with derived theme
    fn merge_themes(&self, base: Theme, derived: Theme) -> Theme {
        // Simple merge - derived theme overrides base theme
        // In a real implementation, you might want more sophisticated merging
        derived
    }
}

impl Theme {
    /// Create default theme
    pub fn default() -> Self {
        Self {
            metadata: ThemeMetadata {
                name: "default".to_string(),
                description: "Default AutoRun theme".to_string(),
                author: "AutoRun Team".to_string(),
                version: "1.0.0".to_string(),
                base: None,
                tags: vec!["default".to_string()],
                variant: ThemeVariant::Dark,
            },
            colors: ColorScheme::default(),
            styles: StyleConfig::default(),
            syntax: SyntaxTheme::default(),
            borders: BorderConfig::default(),
            components: ComponentStyles::default(),
        }
    }
    
    /// Create dark theme
    pub fn dark() -> Self {
        let mut theme = Self::default();
        theme.metadata.name = "dark".to_string();
        theme.metadata.description = "Dark theme for AutoRun".to_string();
        theme.metadata.variant = ThemeVariant::Dark;
        
        // Customize colors for dark theme
        theme.colors.background = Color::Black;
        theme.colors.foreground = Color::White;
        theme.colors.surface = Color::Rgb(32, 32, 32);
        
        theme
    }
    
    /// Create light theme
    pub fn light() -> Self {
        let mut theme = Self::default();
        theme.metadata.name = "light".to_string();
        theme.metadata.description = "Light theme for AutoRun".to_string();
        theme.metadata.variant = ThemeVariant::Light;
        
        // Customize colors for light theme
        theme.colors.background = Color::White;
        theme.colors.foreground = Color::Black;
        theme.colors.surface = Color::Rgb(248, 248, 248);
        
        theme
    }
    
    /// Create high contrast theme
    pub fn high_contrast() -> Self {
        let mut theme = Self::default();
        theme.metadata.name = "high_contrast".to_string();
        theme.metadata.description = "High contrast theme for accessibility".to_string();
        theme.metadata.tags = vec!["accessibility".to_string(), "high-contrast".to_string()];
        
        // High contrast colors
        theme.colors.background = Color::Black;
        theme.colors.foreground = Color::White;
        theme.colors.focus = Color::Yellow;
        theme.colors.selection = Color::Blue;
        theme.colors.error = Color::Red;
        theme.colors.success = Color::Green;
        
        theme
    }
    
    /// Convert to ratatui Style
    pub fn to_ratatui_style(&self, style_def: &StyleDefinition) -> Style {
        let mut style = Style::default();
        
        if let Some(fg) = style_def.fg {
            style = style.fg(fg);
        }
        
        if let Some(bg) = style_def.bg {
            style = style.bg(bg);
        }
        
        for modifier in &style_def.modifiers {
            style = style.add_modifier(modifier.to_ratatui_modifier());
        }
        
        style
    }
}

impl ModifierType {
    /// Convert to ratatui Modifier
    pub fn to_ratatui_modifier(&self) -> Modifier {
        match self {
            ModifierType::Bold => Modifier::BOLD,
            ModifierType::Italic => Modifier::ITALIC,
            ModifierType::Underlined => Modifier::UNDERLINED,
            ModifierType::SlowBlink => Modifier::SLOW_BLINK,
            ModifierType::RapidBlink => Modifier::RAPID_BLINK,
            ModifierType::Reversed => Modifier::REVERSED,
            ModifierType::CrossedOut => Modifier::CROSSED_OUT,
            ModifierType::Dim => Modifier::DIM,
        }
    }
}

impl Default for ColorScheme {
    fn default() -> Self {
        Self {
            primary: Color::Blue,
            secondary: Color::Cyan,
            accent: Color::Magenta,
            background: Color::Black,
            surface: Color::Rgb(16, 16, 16),
            overlay: Color::Rgb(32, 32, 32),
            foreground: Color::White,
            muted: Color::Gray,
            subtle: Color::DarkGray,
            success: Color::Green,
            warning: Color::Yellow,
            error: Color::Red,
            info: Color::Cyan,
            border: Color::Gray,
            selection: Color::Blue,
            highlight: Color::Yellow,
            focus: Color::Cyan,
            user_message: Color::Blue,
            assistant_message: Color::Green,
            code_block: Color::Magenta,
            tool_output: Color::Yellow,
            system_message: Color::Gray,
        }
    }
}

impl Default for StyleConfig {
    fn default() -> Self {
        Self {
            default: StyleDefinition { fg: None, bg: None, modifiers: vec![] },
            heading1: StyleDefinition { fg: None, bg: None, modifiers: vec![ModifierType::Bold] },
            heading2: StyleDefinition { fg: None, bg: None, modifiers: vec![ModifierType::Bold] },
            heading3: StyleDefinition { fg: None, bg: None, modifiers: vec![ModifierType::Bold] },
            bold: StyleDefinition { fg: None, bg: None, modifiers: vec![ModifierType::Bold] },
            italic: StyleDefinition { fg: None, bg: None, modifiers: vec![ModifierType::Italic] },
            underline: StyleDefinition { fg: None, bg: None, modifiers: vec![ModifierType::Underlined] },
            strikethrough: StyleDefinition { fg: None, bg: None, modifiers: vec![ModifierType::CrossedOut] },
            code_inline: StyleDefinition { fg: Some(Color::Cyan), bg: None, modifiers: vec![] },
            code_block: StyleDefinition { fg: Some(Color::Cyan), bg: None, modifiers: vec![] },
            link: StyleDefinition { fg: Some(Color::Blue), bg: None, modifiers: vec![ModifierType::Underlined] },
            quote: StyleDefinition { fg: Some(Color::Gray), bg: None, modifiers: vec![ModifierType::Italic] },
            list_item: StyleDefinition { fg: None, bg: None, modifiers: vec![] },
            list_marker: StyleDefinition { fg: Some(Color::Yellow), bg: None, modifiers: vec![] },
        }
    }
}

impl Default for SyntaxTheme {
    fn default() -> Self {
        Self {
            keyword: Color::Magenta,
            string: Color::Green,
            number: Color::Cyan,
            comment: Color::Gray,
            function: Color::Yellow,
            variable: Color::White,
            type_name: Color::Blue,
            constant: Color::Red,
            operator: Color::White,
            punctuation: Color::Gray,
            preprocessor: Color::Magenta,
            regex: Color::Red,
            escape: Color::Yellow,
            invalid: Color::Red,
        }
    }
}

impl Default for BorderConfig {
    fn default() -> Self {
        Self {
            default: BorderStyle { border_type: BorderType::Plain, color: Color::Gray, thickness: 1 },
            focused: BorderStyle { border_type: BorderType::Plain, color: Color::Cyan, thickness: 1 },
            selected: BorderStyle { border_type: BorderType::Plain, color: Color::Blue, thickness: 1 },
            error: BorderStyle { border_type: BorderType::Plain, color: Color::Red, thickness: 1 },
            warning: BorderStyle { border_type: BorderType::Plain, color: Color::Yellow, thickness: 1 },
            success: BorderStyle { border_type: BorderType::Plain, color: Color::Green, thickness: 1 },
        }
    }
}

impl Default for ComponentStyles {
    fn default() -> Self {
        let default_component = ComponentStyle::default();
        
        Self {
            status_bar: default_component.clone(),
            input_area: default_component.clone(),
            content_area: default_component.clone(),
            completion_popup: default_component.clone(),
            dialog: default_component.clone(),
            progress_bar: default_component.clone(),
            combobox: default_component,
        }
    }
}

impl Default for ComponentStyle {
    fn default() -> Self {
        Self {
            normal: StyleDefinition { fg: None, bg: None, modifiers: vec![] },
            focused: StyleDefinition { fg: Some(Color::Cyan), bg: None, modifiers: vec![] },
            selected: StyleDefinition { fg: Some(Color::Black), bg: Some(Color::Blue), modifiers: vec![] },
            disabled: StyleDefinition { fg: Some(Color::DarkGray), bg: None, modifiers: vec![] },
            hover: StyleDefinition { fg: Some(Color::White), bg: Some(Color::DarkGray), modifiers: vec![] },
            active: StyleDefinition { fg: Some(Color::White), bg: Some(Color::Blue), modifiers: vec![] },
        }
    }
}
