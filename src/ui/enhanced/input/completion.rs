// Auto-completion engine for enhanced input system
// Provides intelligent auto-completion with multiple providers and fuzzy matching

use super::{InputContext, InputResult};
use crate::errors::Result;
use crate::ui::enhanced::Action;
use crossterm::event::{Event, KeyEvent, KeyCode};
use ratatui::prelude::*;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;

/// Auto-completion engine
pub struct CompletionEngine {
    /// Registered completion providers
    providers: Vec<Box<dyn CompletionProvider>>,
    
    /// Current completion state
    state: Option<CompletionState>,
    
    /// Configuration
    config: CompletionConfig,
    
    /// Cache for completion results
    cache: Arc<RwLock<CompletionCache>>,
}

/// Completion provider trait
#[async_trait::async_trait]
pub trait CompletionProvider: Send + Sync {
    /// Get completions for the given context
    async fn get_completions(&self, context: &CompletionContext) -> Result<Vec<CompletionItem>>;
    
    /// Get trigger characters that activate this provider
    fn get_trigger_chars(&self) -> Vec<char>;
    
    /// Get provider priority (higher = more important)
    fn get_priority(&self) -> u8;
    
    /// Check if provider supports fuzzy search
    fn supports_fuzzy_search(&self) -> bool { true }
    
    /// Get provider name
    fn get_name(&self) -> &str;
    
    /// Check if provider can handle the given context
    fn can_handle(&self, context: &CompletionContext) -> bool;
}

/// Completion context
#[derive(Debug, Clone)]
pub struct CompletionContext {
    /// Full input text
    pub text: String,
    
    /// Cursor position
    pub cursor_position: usize,
    
    /// Trigger character that started completion
    pub trigger_char: Option<char>,
    
    /// Trigger position in text
    pub trigger_position: usize,
    
    /// Text after trigger character
    pub query: String,
    
    /// Current input mode
    pub input_mode: super::InputMode,
    
    /// Additional context data
    pub context_data: HashMap<String, String>,
}

/// Completion item
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CompletionItem {
    /// Display text
    pub text: String,
    
    /// Text to insert when selected
    pub insert_text: String,
    
    /// Optional description
    pub description: Option<String>,
    
    /// Item kind/type
    pub kind: CompletionItemKind,
    
    /// Provider that generated this item
    pub provider: String,
    
    /// Priority for sorting (higher = more important)
    pub priority: u8,
    
    /// Additional data
    pub data: HashMap<String, serde_json::Value>,
    
    /// Whether this item requires additional processing
    pub needs_processing: bool,
}

/// Completion item kinds
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum CompletionItemKind {
    /// File path
    File,
    
    /// Directory path
    Directory,
    
    /// Tool/command
    Tool,
    
    /// System command
    Command,
    
    /// Variable
    Variable,
    
    /// Function
    Function,
    
    /// Keyword
    Keyword,
    
    /// Plain text
    Text,
    
    /// Snippet with placeholders
    Snippet,
    
    /// Custom type
    Custom(String),
}

/// Current completion state
#[derive(Debug, Clone)]
pub struct CompletionState {
    /// Available completion items
    pub items: Vec<CompletionItem>,
    
    /// Currently selected item index
    pub selected_index: usize,
    
    /// Completion context
    pub context: CompletionContext,
    
    /// Whether completion popup is visible
    pub visible: bool,
    
    /// Filtered items (after search)
    pub filtered_items: Vec<usize>,
    
    /// Current filter query
    pub filter_query: String,
    
    /// Scroll offset for long lists
    pub scroll_offset: usize,
    
    /// Maximum visible items
    pub max_visible: usize,
}

/// Completion configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CompletionConfig {
    /// Enable auto-completion
    pub enabled: bool,
    
    /// Enable fuzzy matching
    pub fuzzy_matching: bool,
    
    /// Case sensitive matching
    pub case_sensitive: bool,
    
    /// Minimum query length to trigger completion
    pub min_query_length: usize,
    
    /// Maximum number of completion items to show
    pub max_items: usize,
    
    /// Auto-select first item
    pub auto_select_first: bool,
    
    /// Show completion on trigger characters
    pub trigger_on_chars: bool,
    
    /// Completion delay (milliseconds)
    pub delay_ms: u64,
    
    /// Cache completion results
    pub cache_results: bool,
    
    /// Cache TTL (seconds)
    pub cache_ttl: u64,
}

/// Completion cache
struct CompletionCache {
    entries: HashMap<String, CacheEntry>,
    max_size: usize,
}

/// Cache entry
struct CacheEntry {
    items: Vec<CompletionItem>,
    timestamp: std::time::Instant,
    ttl: std::time::Duration,
}

impl CompletionEngine {
    /// Create a new completion engine
    pub fn new() -> Self {
        Self {
            providers: Vec::new(),
            state: None,
            config: CompletionConfig::default(),
            cache: Arc::new(RwLock::new(CompletionCache::new(1000))),
        }
    }
    
    /// Register a completion provider
    pub fn register_provider(&mut self, provider: Box<dyn CompletionProvider>) {
        self.providers.push(provider);
        // Sort providers by priority (highest first)
        self.providers.sort_by(|a, b| b.get_priority().cmp(&a.get_priority()));
    }
    
    /// Trigger completion for the given context
    pub async fn trigger_completion(&mut self, context: CompletionContext) -> Result<bool> {
        if !self.config.enabled {
            return Ok(false);
        }
        
        // Check minimum query length
        if context.query.len() < self.config.min_query_length {
            self.state = None;
            return Ok(false);
        }
        
        // Check cache first
        let cache_key = self.create_cache_key(&context);
        if self.config.cache_results {
            let cache = self.cache.read().await;
            if let Some(cached_items) = cache.get(&cache_key) {
                self.create_completion_state(context, cached_items.clone()).await?;
                return Ok(true);
            }
        }
        
        // Get completions from providers
        let mut all_items = Vec::new();
        
        for provider in &self.providers {
            if provider.can_handle(&context) {
                match provider.get_completions(&context).await {
                    Ok(mut items) => {
                        // Add provider name to items
                        for item in &mut items {
                            item.provider = provider.get_name().to_string();
                        }
                        all_items.extend(items);
                    }
                    Err(e) => {
                        // Log error but continue with other providers
                        tracing::warn!("Completion provider {} failed: {}", provider.get_name(), e);
                    }
                }
            }
        }
        
        // Limit number of items
        if all_items.len() > self.config.max_items {
            all_items.truncate(self.config.max_items);
        }
        
        // Cache results
        if self.config.cache_results && !all_items.is_empty() {
            let mut cache = self.cache.write().await;
            cache.insert(cache_key, all_items.clone(), self.config.cache_ttl);
        }
        
        // Create completion state
        self.create_completion_state(context, all_items).await?;
        
        Ok(self.state.is_some())
    }
    
    /// Create completion state from items
    async fn create_completion_state(&mut self, context: CompletionContext, items: Vec<CompletionItem>) -> Result<()> {
        if items.is_empty() {
            self.state = None;
            return Ok(());
        }
        
        let filtered_items: Vec<usize> = (0..items.len()).collect();
        let selected_index = if self.config.auto_select_first { 0 } else { 0 };
        
        self.state = Some(CompletionState {
            items,
            selected_index,
            context,
            visible: true,
            filtered_items,
            filter_query: String::new(),
            scroll_offset: 0,
            max_visible: 10,
        });
        
        Ok(())
    }
    
    /// Update completion filter
    pub fn update_filter(&mut self, query: &str) {
        if let Some(state) = &mut self.state {
            state.filter_query = query.to_string();
            
            if query.is_empty() {
                state.filtered_items = (0..state.items.len()).collect();
            } else if self.config.fuzzy_matching {
                state.filtered_items = self.fuzzy_filter(&state.items, query);
            } else {
                state.filtered_items = self.simple_filter(&state.items, query);
            }
            
            // Reset selection to first item
            state.selected_index = 0;
            state.scroll_offset = 0;
        }
    }
    
    /// Simple text filtering
    fn simple_filter(&self, items: &[CompletionItem], query: &str) -> Vec<usize> {
        let query = if self.config.case_sensitive {
            query.to_string()
        } else {
            query.to_lowercase()
        };
        
        items
            .iter()
            .enumerate()
            .filter(|(_, item)| {
                let text = if self.config.case_sensitive {
                    item.text.clone()
                } else {
                    item.text.to_lowercase()
                };
                text.contains(&query)
            })
            .map(|(index, _)| index)
            .collect()
    }
    
    /// Fuzzy filtering with scoring
    fn fuzzy_filter(&self, items: &[CompletionItem], query: &str) -> Vec<usize> {
        let query = if self.config.case_sensitive {
            query.to_string()
        } else {
            query.to_lowercase()
        };
        
        let mut scored_items: Vec<(usize, i32)> = items
            .iter()
            .enumerate()
            .filter_map(|(index, item)| {
                let text = if self.config.case_sensitive {
                    item.text.clone()
                } else {
                    item.text.to_lowercase()
                };
                
                let score = self.calculate_fuzzy_score(&text, &query);
                if score > 0 {
                    Some((index, score))
                } else {
                    None
                }
            })
            .collect();
        
        // Sort by score (higher first), then by priority
        scored_items.sort_by(|a, b| {
            let score_cmp = b.1.cmp(&a.1);
            if score_cmp == std::cmp::Ordering::Equal {
                items[b.0].priority.cmp(&items[a.0].priority)
            } else {
                score_cmp
            }
        });
        
        scored_items.into_iter().map(|(index, _)| index).collect()
    }
    
    /// Calculate fuzzy match score
    fn calculate_fuzzy_score(&self, text: &str, query: &str) -> i32 {
        if query.is_empty() {
            return 100;
        }
        
        // Exact match gets highest score
        if text == query {
            return 1000;
        }
        
        // Prefix match gets high score
        if text.starts_with(query) {
            return 500;
        }
        
        // Substring match gets medium score
        if let Some(pos) = text.find(query) {
            return 200 - pos as i32; // Earlier matches score higher
        }
        
        // Character sequence match gets low score
        let mut score = 0;
        let mut text_chars = text.chars().peekable();
        let mut query_chars = query.chars().peekable();
        
        while let (Some(&text_char), Some(&query_char)) = (text_chars.peek(), query_chars.peek()) {
            if text_char == query_char {
                score += 1;
                query_chars.next();
            }
            text_chars.next();
        }
        
        // If we matched all query characters, return the score
        if query_chars.peek().is_none() {
            score
        } else {
            0
        }
    }
    
    /// Get current completion state
    pub fn get_state(&self) -> Option<&CompletionState> {
        self.state.as_ref()
    }
    
    /// Get currently selected completion item
    pub fn get_selected_item(&self) -> Option<&CompletionItem> {
        self.state.as_ref().and_then(|state| {
            state.filtered_items.get(state.selected_index)
                .and_then(|&index| state.items.get(index))
        })
    }
    
    /// Move selection up
    pub fn move_selection_up(&mut self) {
        if let Some(state) = &mut self.state {
            if state.selected_index > 0 {
                state.selected_index -= 1;
                
                // Adjust scroll if needed
                if state.selected_index < state.scroll_offset {
                    state.scroll_offset = state.selected_index;
                }
            }
        }
    }
    
    /// Move selection down
    pub fn move_selection_down(&mut self) {
        if let Some(state) = &mut self.state {
            if state.selected_index + 1 < state.filtered_items.len() {
                state.selected_index += 1;
                
                // Adjust scroll if needed
                if state.selected_index >= state.scroll_offset + state.max_visible {
                    state.scroll_offset = state.selected_index.saturating_sub(state.max_visible - 1);
                }
            }
        }
    }
    
    /// Accept current selection
    pub fn accept_selection(&mut self) -> Option<CompletionItem> {
        if let Some(state) = &mut self.state {
            let item = state.filtered_items.get(state.selected_index)
                .and_then(|&index| state.items.get(index))
                .cloned();
            
            self.state = None; // Close completion
            item
        } else {
            None
        }
    }
    
    /// Cancel completion
    pub fn cancel(&mut self) {
        self.state = None;
    }
    
    /// Check if completion is active
    pub fn is_active(&self) -> bool {
        self.state.is_some()
    }
    
    /// Create cache key for context
    fn create_cache_key(&self, context: &CompletionContext) -> String {
        format!("{}:{}:{}", 
            context.trigger_char.unwrap_or(' '), 
            context.trigger_position, 
            context.query
        )
    }
}

impl CompletionCache {
    fn new(max_size: usize) -> Self {
        Self {
            entries: HashMap::new(),
            max_size,
        }
    }
    
    fn get(&self, key: &str) -> Option<Vec<CompletionItem>> {
        if let Some(entry) = self.entries.get(key) {
            if entry.timestamp.elapsed() < entry.ttl {
                Some(entry.items.clone())
            } else {
                None
            }
        } else {
            None
        }
    }
    
    fn insert(&mut self, key: String, items: Vec<CompletionItem>, ttl_seconds: u64) {
        // Remove expired entries
        self.cleanup_expired();
        
        // Remove oldest entries if at capacity
        while self.entries.len() >= self.max_size {
            if let Some(oldest_key) = self.entries.keys().next().cloned() {
                self.entries.remove(&oldest_key);
            } else {
                break;
            }
        }
        
        let entry = CacheEntry {
            items,
            timestamp: std::time::Instant::now(),
            ttl: std::time::Duration::from_secs(ttl_seconds),
        };
        
        self.entries.insert(key, entry);
    }
    
    fn cleanup_expired(&mut self) {
        let now = std::time::Instant::now();
        self.entries.retain(|_, entry| now.duration_since(entry.timestamp) < entry.ttl);
    }
}

impl Default for CompletionConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            fuzzy_matching: true,
            case_sensitive: false,
            min_query_length: 1,
            max_items: 50,
            auto_select_first: true,
            trigger_on_chars: true,
            delay_ms: 100,
            cache_results: true,
            cache_ttl: 300, // 5 minutes
        }
    }
}

impl CompletionContext {
    /// Create completion context from input
    pub fn from_input(text: &str, cursor_position: usize, input_mode: super::InputMode) -> Self {
        // Find trigger character before cursor
        let before_cursor = &text[..cursor_position];
        let (trigger_char, trigger_position, query) = Self::find_trigger(before_cursor);
        
        Self {
            text: text.to_string(),
            cursor_position,
            trigger_char,
            trigger_position,
            query,
            input_mode,
            context_data: HashMap::new(),
        }
    }
    
    /// Find trigger character and extract query
    fn find_trigger(text: &str) -> (Option<char>, usize, String) {
        let trigger_chars = ['@', '/', '.', ' '];
        
        for (i, ch) in text.char_indices().rev() {
            if trigger_chars.contains(&ch) {
                let query = text[i + ch.len_utf8()..].to_string();
                return (Some(ch), i, query);
            }
        }
        
        // No trigger found, use entire text as query
        (None, 0, text.to_string())
    }
}

impl CompletionItem {
    /// Create a new completion item
    pub fn new(text: String, kind: CompletionItemKind) -> Self {
        Self {
            insert_text: text.clone(),
            text,
            description: None,
            kind,
            provider: String::new(),
            priority: 0,
            data: HashMap::new(),
            needs_processing: false,
        }
    }
    
    /// Set description
    pub fn with_description(mut self, description: String) -> Self {
        self.description = Some(description);
        self
    }
    
    /// Set insert text (different from display text)
    pub fn with_insert_text(mut self, insert_text: String) -> Self {
        self.insert_text = insert_text;
        self
    }
    
    /// Set priority
    pub fn with_priority(mut self, priority: u8) -> Self {
        self.priority = priority;
        self
    }
    
    /// Add custom data
    pub fn with_data(mut self, key: String, value: serde_json::Value) -> Self {
        self.data.insert(key, value);
        self
    }
}
