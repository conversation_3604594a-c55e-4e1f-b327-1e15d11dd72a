// Content blocks module for enhanced TUI
// Provides navigable content blocks with different types and visual distinctions

pub mod block;
pub mod renderer;
pub mod navigator;
pub mod types;

pub use block::{ContentBlock, ContentMetadata, RenderState};
pub use renderer::{ContentB<PERSON><PERSON><PERSON>er, BlockRendererRegistry, RenderContext};
pub use navigator::{ContentNavigator, NavigationResult, NavigationCommand};
pub use types::{ContentBlockType, ErrorType, ErrorSeverity};

use crate::errors::Result;
use crate::ui::enhanced::{Action, NavigationAction};
use chrono::{DateTime, Utc};
use ratatui::prelude::*;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use uuid::Uuid;

/// Content block with metadata and rendering state
#[derive(Debug, Clone)]
pub struct ContentBlock {
    /// Unique identifier
    pub id: Uuid,
    
    /// Block type
    pub block_type: ContentBlockType,
    
    /// Block content
    pub content: String,
    
    /// Block metadata
    pub metadata: ContentMetadata,
    
    /// Creation timestamp
    pub timestamp: DateTime<Utc>,
    
    /// Rendering state
    pub render_state: RenderState,
    
    /// Selection state
    pub is_selected: bool,
    
    /// Focus state
    pub is_focused: bool,
    
    /// Highlight state
    pub is_highlighted: bool,
    
    /// Visibility state
    pub is_visible: bool,
}

/// Content block types with specific properties
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum ContentBlockType {
    /// User input message
    UserMessage,
    
    /// AI assistant response
    AssistantResponse,
    
    /// Code block with optional language
    CodeBlock {
        language: Option<String>,
        syntax_highlighted: bool,
    },
    
    /// Tool execution output
    ToolOutput {
        tool_name: String,
        success: bool,
        execution_time: Option<u64>, // milliseconds
    },
    
    /// Error message
    Error {
        error_type: ErrorType,
        severity: ErrorSeverity,
    },
    
    /// System message
    SystemMessage,
    
    /// Widget container
    Widget {
        widget_id: String,
        widget_type: String,
    },
}

/// Error types for error blocks
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum ErrorType {
    Validation,
    Execution,
    Network,
    Permission,
    Timeout,
    Unknown,
}

/// Error severity levels
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum ErrorSeverity {
    Info,
    Warning,
    Error,
    Critical,
}

/// Content block metadata
#[derive(Debug, Clone, Default)]
pub struct ContentMetadata {
    /// Author/source
    pub author: Option<String>,
    
    /// Tags for categorization
    pub tags: Vec<String>,
    
    /// Custom properties
    pub properties: HashMap<String, String>,
    
    /// Content length
    pub content_length: usize,
    
    /// Line count
    pub line_count: usize,
    
    /// Word count
    pub word_count: usize,
}

/// Rendering state for content blocks
#[derive(Debug, Clone, Default)]
pub struct RenderState {
    /// Calculated height
    pub height: u16,
    
    /// Calculated width
    pub width: u16,
    
    /// Scroll position within block
    pub scroll_offset: u16,
    
    /// Whether content is wrapped
    pub is_wrapped: bool,
    
    /// Whether content is truncated
    pub is_truncated: bool,
    
    /// Last render area
    pub last_area: Option<Rect>,
    
    /// Dirty flag for re-rendering
    pub is_dirty: bool,
}

/// Content navigator for managing block navigation
pub struct ContentNavigator {
    /// All content blocks
    blocks: Vec<ContentBlock>,
    
    /// Block index for quick lookup
    block_index: HashMap<Uuid, usize>,
    
    /// Currently focused block
    focused_block: Option<Uuid>,
    
    /// Selected blocks
    selected_blocks: Vec<Uuid>,
    
    /// Scroll position
    scroll_offset: usize,
    
    /// Viewport height
    viewport_height: u16,
    
    /// Search state
    search_state: SearchState,
    
    /// Filter state
    filter_state: FilterState,
}

/// Search state for content navigation
#[derive(Debug, Clone, Default)]
pub struct SearchState {
    /// Search query
    pub query: String,
    
    /// Search results
    pub results: Vec<Uuid>,
    
    /// Current result index
    pub current_result: Option<usize>,
    
    /// Case sensitive search
    pub case_sensitive: bool,
    
    /// Regex search
    pub regex: bool,
    
    /// Search in content only or include metadata
    pub content_only: bool,
}

/// Filter state for content display
#[derive(Debug, Clone, Default)]
pub struct FilterState {
    /// Block type filter
    pub block_types: Option<Vec<ContentBlockType>>,
    
    /// Tag filter
    pub tags: Option<Vec<String>>,
    
    /// Author filter
    pub author: Option<String>,
    
    /// Date range filter
    pub date_range: Option<(DateTime<Utc>, DateTime<Utc>)>,
    
    /// Custom filter function
    pub custom_filter: Option<String>, // Serialized filter expression
}

/// Navigation commands
#[derive(Debug, Clone, PartialEq)]
pub enum NavigationCommand {
    /// Move to next block
    NextBlock,
    
    /// Move to previous block
    PreviousBlock,
    
    /// Move to first block
    FirstBlock,
    
    /// Move to last block
    LastBlock,
    
    /// Move to specific block
    GotoBlock(Uuid),
    
    /// Select current block
    SelectCurrent,
    
    /// Toggle selection of current block
    ToggleSelection,
    
    /// Select all blocks
    SelectAll,
    
    /// Clear selection
    ClearSelection,
    
    /// Scroll up
    ScrollUp(usize),
    
    /// Scroll down
    ScrollDown(usize),
    
    /// Page up
    PageUp,
    
    /// Page down
    PageDown,
    
    /// Search
    Search(String),
    
    /// Next search result
    NextSearchResult,
    
    /// Previous search result
    PreviousSearchResult,
    
    /// Clear search
    ClearSearch,
    
    /// Apply filter
    ApplyFilter(FilterState),
    
    /// Clear filter
    ClearFilter,
}

/// Navigation result
#[derive(Debug, Clone, PartialEq)]
pub enum NavigationResult {
    /// Navigation successful
    Success,
    
    /// Navigation successful with action
    SuccessWithAction(Action),
    
    /// No change (already at target)
    NoChange,
    
    /// Invalid navigation (e.g., no blocks)
    Invalid,
    
    /// Block not found
    BlockNotFound(Uuid),
    
    /// Search completed
    SearchCompleted {
        results: usize,
        current: Option<usize>,
    },
    
    /// Filter applied
    FilterApplied {
        visible_blocks: usize,
        total_blocks: usize,
    },
}

impl ContentBlock {
    /// Create a new content block
    pub fn new(block_type: ContentBlockType, content: String) -> Self {
        let metadata = ContentMetadata {
            content_length: content.len(),
            line_count: content.lines().count(),
            word_count: content.split_whitespace().count(),
            ..Default::default()
        };
        
        Self {
            id: Uuid::new_v4(),
            block_type,
            content,
            metadata,
            timestamp: Utc::now(),
            render_state: RenderState::default(),
            is_selected: false,
            is_focused: false,
            is_highlighted: false,
            is_visible: true,
        }
    }
    
    /// Create a user message block
    pub fn user_message(content: String) -> Self {
        Self::new(ContentBlockType::UserMessage, content)
    }
    
    /// Create an assistant response block
    pub fn assistant_response(content: String) -> Self {
        Self::new(ContentBlockType::AssistantResponse, content)
    }
    
    /// Create a code block
    pub fn code_block(content: String, language: Option<String>) -> Self {
        Self::new(
            ContentBlockType::CodeBlock {
                language,
                syntax_highlighted: false,
            },
            content,
        )
    }
    
    /// Create a tool output block
    pub fn tool_output(content: String, tool_name: String, success: bool) -> Self {
        Self::new(
            ContentBlockType::ToolOutput {
                tool_name,
                success,
                execution_time: None,
            },
            content,
        )
    }
    
    /// Create an error block
    pub fn error(content: String, error_type: ErrorType, severity: ErrorSeverity) -> Self {
        Self::new(
            ContentBlockType::Error {
                error_type,
                severity,
            },
            content,
        )
    }
    
    /// Create a system message block
    pub fn system_message(content: String) -> Self {
        Self::new(ContentBlockType::SystemMessage, content)
    }
    
    /// Get block type name for display
    pub fn get_type_name(&self) -> &'static str {
        match self.block_type {
            ContentBlockType::UserMessage => "User",
            ContentBlockType::AssistantResponse => "Assistant",
            ContentBlockType::CodeBlock { .. } => "Code",
            ContentBlockType::ToolOutput { .. } => "Tool",
            ContentBlockType::Error { .. } => "Error",
            ContentBlockType::SystemMessage => "System",
            ContentBlockType::Widget { .. } => "Widget",
        }
    }
    
    /// Get block color for styling
    pub fn get_color(&self) -> Color {
        match self.block_type {
            ContentBlockType::UserMessage => Color::Blue,
            ContentBlockType::AssistantResponse => Color::Green,
            ContentBlockType::CodeBlock { .. } => Color::Magenta,
            ContentBlockType::ToolOutput { success: true, .. } => Color::Yellow,
            ContentBlockType::ToolOutput { success: false, .. } => Color::Red,
            ContentBlockType::Error { severity: ErrorSeverity::Critical, .. } => Color::Red,
            ContentBlockType::Error { severity: ErrorSeverity::Error, .. } => Color::LightRed,
            ContentBlockType::Error { severity: ErrorSeverity::Warning, .. } => Color::Yellow,
            ContentBlockType::Error { severity: ErrorSeverity::Info, .. } => Color::Cyan,
            ContentBlockType::SystemMessage => Color::Gray,
            ContentBlockType::Widget { .. } => Color::White,
        }
    }
    
    /// Get block border style
    pub fn get_border_style(&self) -> Style {
        let color = self.get_color();
        let mut style = Style::default().fg(color);
        
        if self.is_focused {
            style = style.add_modifier(Modifier::BOLD);
        }
        
        if self.is_selected {
            style = style.bg(color.into()).fg(Color::Black);
        }
        
        style
    }
    
    /// Check if block matches search query
    pub fn matches_search(&self, query: &str, case_sensitive: bool, content_only: bool) -> bool {
        let query = if case_sensitive { query.to_string() } else { query.to_lowercase() };
        
        // Search in content
        let content = if case_sensitive {
            self.content.clone()
        } else {
            self.content.to_lowercase()
        };
        
        if content.contains(&query) {
            return true;
        }
        
        if !content_only {
            // Search in metadata
            if let Some(author) = &self.metadata.author {
                let author = if case_sensitive {
                    author.clone()
                } else {
                    author.to_lowercase()
                };
                if author.contains(&query) {
                    return true;
                }
            }
            
            // Search in tags
            for tag in &self.metadata.tags {
                let tag = if case_sensitive {
                    tag.clone()
                } else {
                    tag.to_lowercase()
                };
                if tag.contains(&query) {
                    return true;
                }
            }
        }
        
        false
    }
    
    /// Check if block matches filter
    pub fn matches_filter(&self, filter: &FilterState) -> bool {
        // Check block type filter
        if let Some(ref types) = filter.block_types {
            if !types.contains(&self.block_type) {
                return false;
            }
        }
        
        // Check tag filter
        if let Some(ref tags) = filter.tags {
            if !tags.iter().any(|tag| self.metadata.tags.contains(tag)) {
                return false;
            }
        }
        
        // Check author filter
        if let Some(ref author) = filter.author {
            if self.metadata.author.as_ref() != Some(author) {
                return false;
            }
        }
        
        // Check date range filter
        if let Some((start, end)) = filter.date_range {
            if self.timestamp < start || self.timestamp > end {
                return false;
            }
        }
        
        true
    }
    
    /// Update render state
    pub fn update_render_state(&mut self, area: Rect) {
        self.render_state.last_area = Some(area);
        self.render_state.width = area.width;
        
        // Calculate height based on content and wrapping
        let lines = self.content.lines().count() as u16;
        let wrapped_lines = if area.width > 4 {
            self.content.lines()
                .map(|line| ((line.len() as u16).saturating_sub(1) / (area.width - 4)) + 1)
                .sum::<u16>()
        } else {
            lines
        };
        
        self.render_state.height = wrapped_lines + 2; // +2 for borders
        self.render_state.is_wrapped = wrapped_lines > lines;
        self.render_state.is_dirty = false;
    }
    
    /// Mark as dirty for re-rendering
    pub fn mark_dirty(&mut self) {
        self.render_state.is_dirty = true;
    }
}

impl ContentNavigator {
    /// Create a new content navigator
    pub fn new() -> Self {
        Self {
            blocks: Vec::new(),
            block_index: HashMap::new(),
            focused_block: None,
            selected_blocks: Vec::new(),
            scroll_offset: 0,
            viewport_height: 0,
            search_state: SearchState::default(),
            filter_state: FilterState::default(),
        }
    }
    
    /// Add a content block
    pub fn add_block(&mut self, mut block: ContentBlock) -> Uuid {
        let id = block.id;
        let index = self.blocks.len();
        
        // If this is the first block, focus it
        if self.blocks.is_empty() {
            block.is_focused = true;
            self.focused_block = Some(id);
        }
        
        self.blocks.push(block);
        self.block_index.insert(id, index);
        
        id
    }
    
    /// Remove a content block
    pub fn remove_block(&mut self, id: Uuid) -> Option<ContentBlock> {
        if let Some(&index) = self.block_index.get(&id) {
            let block = self.blocks.remove(index);
            self.block_index.remove(&id);
            
            // Update indices
            for (_, idx) in self.block_index.iter_mut() {
                if *idx > index {
                    *idx -= 1;
                }
            }
            
            // Update focus if removed block was focused
            if self.focused_block == Some(id) {
                self.focused_block = None;
                if !self.blocks.is_empty() {
                    let new_index = index.min(self.blocks.len() - 1);
                    let new_id = self.blocks[new_index].id;
                    self.set_focus(Some(new_id));
                }
            }
            
            // Remove from selection
            self.selected_blocks.retain(|&selected_id| selected_id != id);
            
            Some(block)
        } else {
            None
        }
    }
    
    /// Get all visible blocks (after filtering)
    pub fn get_visible_blocks(&self) -> Vec<&ContentBlock> {
        self.blocks.iter().filter(|block| block.is_visible).collect()
    }
    
    /// Get focused block
    pub fn get_focused_block(&self) -> Option<&ContentBlock> {
        self.focused_block
            .and_then(|id| self.block_index.get(&id))
            .and_then(|&index| self.blocks.get(index))
    }
    
    /// Set viewport height
    pub fn set_viewport_height(&mut self, height: u16) {
        self.viewport_height = height;
    }
    
    /// Execute navigation command
    pub fn execute_command(&mut self, command: NavigationCommand) -> NavigationResult {
        match command {
            NavigationCommand::NextBlock => self.move_focus_next(),
            NavigationCommand::PreviousBlock => self.move_focus_previous(),
            NavigationCommand::FirstBlock => self.move_focus_first(),
            NavigationCommand::LastBlock => self.move_focus_last(),
            NavigationCommand::GotoBlock(id) => self.move_focus_to(id),
            NavigationCommand::SelectCurrent => self.select_current(),
            NavigationCommand::ToggleSelection => self.toggle_selection_current(),
            NavigationCommand::SelectAll => self.select_all(),
            NavigationCommand::ClearSelection => self.clear_selection(),
            NavigationCommand::ScrollUp(lines) => self.scroll_up(lines),
            NavigationCommand::ScrollDown(lines) => self.scroll_down(lines),
            NavigationCommand::PageUp => self.page_up(),
            NavigationCommand::PageDown => self.page_down(),
            NavigationCommand::Search(query) => self.search(query),
            NavigationCommand::NextSearchResult => self.next_search_result(),
            NavigationCommand::PreviousSearchResult => self.previous_search_result(),
            NavigationCommand::ClearSearch => self.clear_search(),
            NavigationCommand::ApplyFilter(filter) => self.apply_filter(filter),
            NavigationCommand::ClearFilter => self.clear_filter(),
        }
    }
    
    /// Move focus to next block
    fn move_focus_next(&mut self) -> NavigationResult {
        if let Some(current_id) = self.focused_block {
            if let Some(&current_index) = self.block_index.get(&current_id) {
                let visible_blocks: Vec<_> = self.blocks.iter()
                    .enumerate()
                    .filter(|(_, block)| block.is_visible)
                    .collect();
                
                if let Some(current_pos) = visible_blocks.iter().position(|(i, _)| *i == current_index) {
                    if current_pos + 1 < visible_blocks.len() {
                        let (next_index, _) = visible_blocks[current_pos + 1];
                        let next_id = self.blocks[next_index].id;
                        self.set_focus(Some(next_id));
                        return NavigationResult::Success;
                    }
                }
            }
        }
        NavigationResult::NoChange
    }
    
    /// Move focus to previous block
    fn move_focus_previous(&mut self) -> NavigationResult {
        if let Some(current_id) = self.focused_block {
            if let Some(&current_index) = self.block_index.get(&current_id) {
                let visible_blocks: Vec<_> = self.blocks.iter()
                    .enumerate()
                    .filter(|(_, block)| block.is_visible)
                    .collect();
                
                if let Some(current_pos) = visible_blocks.iter().position(|(i, _)| *i == current_index) {
                    if current_pos > 0 {
                        let (prev_index, _) = visible_blocks[current_pos - 1];
                        let prev_id = self.blocks[prev_index].id;
                        self.set_focus(Some(prev_id));
                        return NavigationResult::Success;
                    }
                }
            }
        }
        NavigationResult::NoChange
    }
    
    /// Set focus to a specific block
    fn set_focus(&mut self, block_id: Option<Uuid>) {
        // Clear current focus
        if let Some(current_id) = self.focused_block {
            if let Some(&index) = self.block_index.get(&current_id) {
                self.blocks[index].is_focused = false;
            }
        }
        
        // Set new focus
        self.focused_block = block_id;
        if let Some(id) = block_id {
            if let Some(&index) = self.block_index.get(&id) {
                self.blocks[index].is_focused = true;
            }
        }
    }
    
    // Additional navigation methods would be implemented here...
    // (move_focus_first, move_focus_last, select_current, etc.)
    
    /// Move focus to first visible block
    fn move_focus_first(&mut self) -> NavigationResult {
        let first_visible = self.blocks.iter()
            .find(|block| block.is_visible)
            .map(|block| block.id);

        if let Some(id) = first_visible {
            self.set_focus(Some(id));
            NavigationResult::Success
        } else {
            NavigationResult::Invalid
        }
    }

    /// Move focus to last visible block
    fn move_focus_last(&mut self) -> NavigationResult {
        let last_visible = self.blocks.iter()
            .rev()
            .find(|block| block.is_visible)
            .map(|block| block.id);

        if let Some(id) = last_visible {
            self.set_focus(Some(id));
            NavigationResult::Success
        } else {
            NavigationResult::Invalid
        }
    }

    /// Move focus to specific block
    fn move_focus_to(&mut self, id: Uuid) -> NavigationResult {
        if let Some(&index) = self.block_index.get(&id) {
            if self.blocks[index].is_visible {
                self.set_focus(Some(id));
                NavigationResult::Success
            } else {
                NavigationResult::Invalid
            }
        } else {
            NavigationResult::BlockNotFound(id)
        }
    }

    /// Select current focused block
    fn select_current(&mut self) -> NavigationResult {
        if let Some(id) = self.focused_block {
            if !self.selected_blocks.contains(&id) {
                self.selected_blocks.push(id);
                if let Some(&index) = self.block_index.get(&id) {
                    self.blocks[index].is_selected = true;
                }
            }
            NavigationResult::Success
        } else {
            NavigationResult::Invalid
        }
    }

    /// Toggle selection of current focused block
    fn toggle_selection_current(&mut self) -> NavigationResult {
        if let Some(id) = self.focused_block {
            if let Some(pos) = self.selected_blocks.iter().position(|&x| x == id) {
                self.selected_blocks.remove(pos);
                if let Some(&index) = self.block_index.get(&id) {
                    self.blocks[index].is_selected = false;
                }
            } else {
                self.selected_blocks.push(id);
                if let Some(&index) = self.block_index.get(&id) {
                    self.blocks[index].is_selected = true;
                }
            }
            NavigationResult::Success
        } else {
            NavigationResult::Invalid
        }
    }

    /// Select all visible blocks
    fn select_all(&mut self) -> NavigationResult {
        self.selected_blocks.clear();
        for block in &mut self.blocks {
            if block.is_visible {
                self.selected_blocks.push(block.id);
                block.is_selected = true;
            }
        }
        NavigationResult::Success
    }

    /// Clear all selections
    fn clear_selection(&mut self) -> NavigationResult {
        for id in &self.selected_blocks {
            if let Some(&index) = self.block_index.get(id) {
                self.blocks[index].is_selected = false;
            }
        }
        self.selected_blocks.clear();
        NavigationResult::Success
    }

    /// Scroll up by specified lines
    fn scroll_up(&mut self, lines: usize) -> NavigationResult {
        self.scroll_offset = self.scroll_offset.saturating_sub(lines);
        NavigationResult::Success
    }

    /// Scroll down by specified lines
    fn scroll_down(&mut self, lines: usize) -> NavigationResult {
        let max_scroll = self.blocks.len().saturating_sub(self.viewport_height as usize);
        self.scroll_offset = (self.scroll_offset + lines).min(max_scroll);
        NavigationResult::Success
    }

    /// Page up
    fn page_up(&mut self) -> NavigationResult {
        let page_size = (self.viewport_height as usize).saturating_sub(1);
        self.scroll_up(page_size)
    }

    /// Page down
    fn page_down(&mut self) -> NavigationResult {
        let page_size = (self.viewport_height as usize).saturating_sub(1);
        self.scroll_down(page_size)
    }

    /// Search for content
    fn search(&mut self, query: String) -> NavigationResult {
        self.search_state.query = query.clone();
        self.search_state.results.clear();
        self.search_state.current_result = None;

        // Find matching blocks
        for (index, block) in self.blocks.iter().enumerate() {
            if block.matches_search(&query, self.search_state.case_sensitive, self.search_state.content_only) {
                self.search_state.results.push(block.id);
            }
        }

        // Move to first result if any
        if !self.search_state.results.is_empty() {
            self.search_state.current_result = Some(0);
            let first_result = self.search_state.results[0];
            self.set_focus(Some(first_result));
        }

        NavigationResult::SearchCompleted {
            results: self.search_state.results.len(),
            current: self.search_state.current_result,
        }
    }

    /// Move to next search result
    fn next_search_result(&mut self) -> NavigationResult {
        if self.search_state.results.is_empty() {
            return NavigationResult::Invalid;
        }

        let current = self.search_state.current_result.unwrap_or(0);
        let next = (current + 1) % self.search_state.results.len();
        self.search_state.current_result = Some(next);

        let result_id = self.search_state.results[next];
        self.set_focus(Some(result_id));

        NavigationResult::SearchCompleted {
            results: self.search_state.results.len(),
            current: Some(next),
        }
    }

    /// Move to previous search result
    fn previous_search_result(&mut self) -> NavigationResult {
        if self.search_state.results.is_empty() {
            return NavigationResult::Invalid;
        }

        let current = self.search_state.current_result.unwrap_or(0);
        let prev = if current == 0 {
            self.search_state.results.len() - 1
        } else {
            current - 1
        };
        self.search_state.current_result = Some(prev);

        let result_id = self.search_state.results[prev];
        self.set_focus(Some(result_id));

        NavigationResult::SearchCompleted {
            results: self.search_state.results.len(),
            current: Some(prev),
        }
    }

    /// Clear search
    fn clear_search(&mut self) -> NavigationResult {
        self.search_state = SearchState::default();
        NavigationResult::Success
    }

    /// Apply filter
    fn apply_filter(&mut self, filter: FilterState) -> NavigationResult {
        self.filter_state = filter;
        let mut visible_count = 0;

        for block in &mut self.blocks {
            block.is_visible = block.matches_filter(&self.filter_state);
            if block.is_visible {
                visible_count += 1;
            }
        }

        NavigationResult::FilterApplied {
            visible_blocks: visible_count,
            total_blocks: self.blocks.len(),
        }
    }

    /// Clear filter
    fn clear_filter(&mut self) -> NavigationResult {
        self.filter_state = FilterState::default();
        for block in &mut self.blocks {
            block.is_visible = true;
        }

        NavigationResult::FilterApplied {
            visible_blocks: self.blocks.len(),
            total_blocks: self.blocks.len(),
        }
    }
}

impl Default for ContentNavigator {
    fn default() -> Self {
        Self::new()
    }
}
