# Specification: [Project/Task Title]

## 1. Goal/Objective

Clearly state the primary purpose and desired outcome of this coding task. What problem are we trying to solve or what feature are we trying to build?

## 2. Input

Describe any initial context, existing code, data, or resources the agent needs to start.

## 3. Output

Define what the final deliverable(s) should be. Be specific about format, location, and content.
*   [e.g., Python code file: `src/main.py`]
*   [e.g., Unit tests: `tests/test_main.py`]
*   [e.g., Documentation update: `docs/README.md`]

## 4. Constraints

List any critical limitations or non-negotiable requirements.
*   [e.g., Must use Python 3.9+]
*   [e.g., Must use the Flask framework]
*   [e.g., Must not introduce new external dependencies]
*   [e.g., Performance target: Process X requests/second]
*   [e.g., Adhere to PEP 8 style guide]