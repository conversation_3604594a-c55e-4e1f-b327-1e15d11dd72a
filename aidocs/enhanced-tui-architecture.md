# Enhanced TUI Architecture Design

## Overview

This document outlines the comprehensive design for enhancing AutoRun-RS's TUI interface to provide a modern coding-like experience with navigable content blocks, REPL-like interface, advanced input system, and configurable architecture.

## Current Architecture Analysis

### Existing Components
- **TuiInterface**: Main UI controller with basic modes (Normal, Input, Widget)
- **Renderer**: Handles rendering with basic and advanced layouts
- **Widget System**: AI-powered widget framework with factory pattern
- **Completion System**: Basic tab completion and mention completion
- **Message Passing**: mpsc channels between UI and AppCore
- **Async Integration**: Tokio-based async architecture

### Limitations to Address
1. Simple list-based message display without content navigation
2. Basic input handling without REPL features
3. Limited completion system without advanced UI components
4. No Vim keybinding support
5. No theming or configuration system
6. Basic widget integration

## Enhanced Architecture Design

### 1. Content Block System

#### Content Block Types
```rust
#[derive(Debug, <PERSON>lone, PartialEq)]
pub enum ContentBlockType {
    UserMessage,
    AssistantResponse,
    CodeBlock { language: Option<String> },
    ToolOutput { tool_name: String },
    Error { error_type: ErrorType },
    SystemMessage,
    Widget { widget_id: String },
}

#[derive(Debug, Clone)]
pub struct ContentBlock {
    pub id: Uuid,
    pub block_type: ContentBlockType,
    pub content: String,
    pub metadata: ContentMetadata,
    pub timestamp: DateTime<Utc>,
    pub is_selected: bool,
    pub is_highlighted: bool,
}
```

#### Navigation System
- **Block Navigation**: j/k or arrow keys to navigate between blocks
- **Block Selection**: Space to select/deselect blocks
- **Block Actions**: Enter to interact with selected block
- **Visual Indicators**: Different colors/styles for each block type

### 2. REPL-like Interface

#### Command History
```rust
pub struct CommandHistory {
    history: VecDeque<String>,
    current_index: Option<usize>,
    max_size: usize,
}
```

#### Execution Status
```rust
#[derive(Debug, Clone)]
pub enum ExecutionStatus {
    Idle,
    Processing { message: String, progress: Option<f32> },
    Completed { duration: Duration },
    Failed { error: String },
}
```

#### Multi-line Input Support
- **Line Continuation**: Shift+Enter for new line
- **Smart Indentation**: Auto-indent based on context
- **Syntax Highlighting**: Basic highlighting for code blocks

### 3. Advanced Input System

#### Auto-completion Framework
```rust
pub trait CompletionProvider {
    async fn get_completions(&self, context: &CompletionContext) -> Vec<CompletionItem>;
    fn get_trigger_chars(&self) -> Vec<char>;
    fn get_priority(&self) -> u8;
}

pub struct CompletionEngine {
    providers: Vec<Box<dyn CompletionProvider>>,
    active_completion: Option<CompletionState>,
}
```

#### Vim Keybinding System
```rust
#[derive(Debug, Clone)]
pub enum VimMode {
    Normal,
    Insert,
    Visual,
    Command,
}

pub struct VimKeybindings {
    mode: VimMode,
    key_sequence: Vec<KeyEvent>,
    pending_operator: Option<VimOperator>,
}
```

#### Combobox/Dropdown Components
- **Fuzzy Search**: Real-time filtering of options
- **Keyboard Navigation**: Arrow keys, Tab, Enter
- **Mouse Support**: Click to select
- **Accessibility**: Screen reader compatible

### 4. Configurable Architecture

#### Theme System
```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Theme {
    pub name: String,
    pub colors: ColorScheme,
    pub styles: StyleConfig,
    pub borders: BorderConfig,
}

pub struct ThemeManager {
    themes: HashMap<String, Theme>,
    active_theme: String,
    hot_reload: bool,
}
```

#### Keybinding Configuration
```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct KeybindingConfig {
    pub global: HashMap<String, Action>,
    pub mode_specific: HashMap<Mode, HashMap<String, Action>>,
    pub vim_enabled: bool,
}
```

#### Hot-reloading System
- **File Watching**: Monitor config files for changes
- **Live Updates**: Apply changes without restart
- **Validation**: Ensure config integrity before applying

## Component Architecture

### Enhanced UI Module Structure
```
src/ui/
├── mod.rs                    # Main UI module
├── app_interface.rs          # Enhanced TUI interface
├── renderer/                 # Rendering system
│   ├── mod.rs
│   ├── content_blocks.rs     # Content block rendering
│   ├── input_area.rs         # Advanced input rendering
│   └── status_bar.rs         # Enhanced status bar
├── components/               # UI components
│   ├── mod.rs
│   ├── combobox.rs          # Dropdown/combobox component
│   ├── completion_popup.rs   # Auto-completion UI
│   ├── progress_bar.rs       # Progress indicators
│   └── dialog.rs            # Modal dialogs
├── input/                    # Input handling
│   ├── mod.rs
│   ├── vim_bindings.rs       # Vim keybinding system
│   ├── completion.rs         # Completion engine
│   └── history.rs           # Command history
├── navigation/               # Navigation system
│   ├── mod.rs
│   ├── content_navigator.rs  # Content block navigation
│   └── focus_manager.rs     # Focus management
├── config/                   # UI configuration
│   ├── mod.rs
│   ├── theme.rs             # Theme system
│   ├── keybindings.rs       # Keybinding config
│   └── hot_reload.rs        # Hot-reloading
└── accessibility/            # Accessibility features
    ├── mod.rs
    └── screen_reader.rs     # Screen reader support
```

### Integration Points

#### Message Flow
1. **User Input** → Input Handler → Command Parser → AppCore
2. **Agent Response** → Content Block Creator → Renderer
3. **Tool Output** → Content Block Creator → Renderer
4. **UI Events** → Event Handler → State Manager

#### State Management
```rust
pub struct EnhancedTuiState {
    pub content_blocks: Vec<ContentBlock>,
    pub selected_block: Option<Uuid>,
    pub navigation_state: NavigationState,
    pub input_state: InputState,
    pub completion_state: Option<CompletionState>,
    pub execution_status: ExecutionStatus,
    pub theme: Theme,
    pub keybindings: KeybindingConfig,
}
```

## Performance Considerations

### Rendering Optimization
- **Incremental Rendering**: Only re-render changed components
- **Virtual Scrolling**: Handle large message histories efficiently
- **Lazy Loading**: Load content blocks on demand

### Memory Management
- **Content Block Pooling**: Reuse content block objects
- **History Limits**: Configurable message history limits
- **Efficient Data Structures**: Use appropriate collections

## Backward Compatibility

### Migration Strategy
1. **Gradual Enhancement**: Implement features incrementally
2. **Feature Flags**: Allow toggling between old/new UI
3. **API Preservation**: Maintain existing public interfaces
4. **Configuration Migration**: Auto-migrate old configs

### Compatibility Layer
```rust
pub struct CompatibilityLayer {
    legacy_mode: bool,
    feature_flags: HashMap<String, bool>,
}
```

## Implementation Phases

### Phase 1: Foundation (Weeks 1-2)
- Enhanced content block system
- Basic navigation between blocks
- Improved message rendering

### Phase 2: Input Enhancement (Weeks 3-4)
- REPL-like interface
- Command history
- Multi-line input support

### Phase 3: Advanced Features (Weeks 5-6)
- Auto-completion system
- Vim keybindings
- Combobox components

### Phase 4: Configuration (Weeks 7-8)
- Theme system
- Keybinding configuration
- Hot-reloading

### Phase 5: Polish & Testing (Weeks 9-10)
- Accessibility features
- Performance optimization
- Comprehensive testing

## Next Steps

1. Create detailed technical specification
2. Implement core UI components
3. Build content block navigation system
4. Develop REPL interface
5. Add advanced input features
6. Implement configuration system
7. Test and integrate all components
