@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
	--background: 0 0% 100%;
	--foreground: 0 0% 3.9%;
	--card: 0 0% 100%;
	--card-foreground: 0 0% 3.9%;
	--popover: 0 0% 100%;
	--popover-foreground: 0 0% 3.9%;
	--primary: 0 0% 9%;
	--primary-foreground: 0 0% 98%;
	--secondary: 0 0% 96.1%;
	--secondary-foreground: 0 0% 9%;
	--muted: 0 0% 96.1%;
	--muted-foreground: 0 0% 45.1%;
	--accent: 0 0% 96.1%;
	--accent-foreground: 0 0% 9%;
	--destructive: 0 84.2% 60.2%;
	--destructive-foreground: 0 0% 98%;
	--border: 0 0% 89.8%;
	--input: 0 0% 89.8%;
	--ring: 0 0% 3.9%;
	--radius: 0.5rem;

	--chart-1: 0 100% 50%;
	--chart-2: 29 100% 50%;
	--chart-3: 51 100% 50%;
	--chart-4: 83 100% 50%;
	--chart-5: 115 100% 50%;
	--chart-6: 147 100% 50%;
	--chart-7: 168 100% 50%;
	--chart-8: 196 100% 50%;
	--chart-9: 224 100% 50%;
	--chart-10: 261 100% 50%;
	--chart-11: 279 100% 50%;
	--chart-12: 298 100% 50%;
	--chart-13: 324 100% 50%;
	--chart-14: 358 100% 50%;
}

.dark {
	--background: 0 0% 0%;
	--foreground: 0 0% 98%;
	--card: 0 0% 3.9%;
	--card-foreground: 0 0% 98%;
	--popover: 0 0% 3.9%;
	--popover-foreground: 0 0% 98%;
	--primary: 0 0% 98%;
	--primary-foreground: 0 0% 9%;
	--secondary: 0 0% 14.9%;
	--secondary-foreground: 0 0% 98%;
	--muted: 0 0% 14.9%;
	--muted-foreground: 0 0% 63.9%;
	--accent: 0 0% 14.9%;
	--accent-foreground: 0 0% 98%;
	--destructive: 0 62.8% 30.6%;
	--destructive-foreground: 0 0% 98%;
	--border: 0 0% 14.9%;
	--input: 0 0% 14.9%;
	--ring: 0 0% 83.1%;
}

@layer base {
	* {
		@apply border-border;
	}
	body {
		@apply bg-background text-foreground;
	}
}
