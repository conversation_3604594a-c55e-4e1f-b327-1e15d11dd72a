name: Preview roocode.com

on:
    push:
        branches-ignore:
            - main
        paths:
            - 'apps/web-roo-code/**'
    workflow_dispatch:

env:
    VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
    VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}

jobs:
    preview:
        runs-on: ubuntu-latest
        if: ${{ secrets.VERCEL_TOKEN != '' }}
        steps:
            - name: Checkout code
              uses: actions/checkout@v4
            - name: Setup Node.js and pnpm
              uses: ./.github/actions/setup-node-pnpm
            - name: Install Vercel CLI
              run: npm install --global vercel@canary
            - name: Pull Vercel Environment Information
              run: npx vercel pull --yes --environment=preview --token=${{ secrets.VERCEL_TOKEN }}
            - name: Build Project Artifacts
              run: npx vercel build --token=${{ secrets.VERCEL_TOKEN }}
            - name: Deploy Project Artifacts to Vercel
              run: npx vercel deploy --prebuilt --token=${{ secrets.VERCEL_TOKEN }}
