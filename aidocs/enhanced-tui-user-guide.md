# Enhanced TUI User Guide

## Overview

The Enhanced TUI provides a modern, coding-focused interface for AutoRun-RS with navigable content blocks, REPL-like functionality, advanced input features, and full customization capabilities.

## Getting Started

### Enabling Enhanced TUI

The enhanced TUI is enabled by default. To switch between modes:

```bash
# Use enhanced TUI (default)
cargo run

# Use legacy TUI
cargo run -- --legacy-ui

# Force enhanced TUI
cargo run -- --enhanced-ui
```

### First-time Setup

1. **Theme Selection**: Choose your preferred theme
2. **Keybinding Mode**: Select between default or Vim keybindings
3. **Accessibility**: Configure screen reader support if needed

## Interface Overview

### Main Areas

1. **Content Area**: Displays conversation messages as navigable blocks
2. **Input Area**: Enhanced input with auto-completion and multi-line support
3. **Status Bar**: Shows current mode, execution status, and help
4. **Sidebar** (optional): Widget area for tools and additional information

### Content Block Types

- **User Messages**: Your input messages (blue border)
- **Assistant Responses**: AI responses (green border)
- **Code Blocks**: Syntax-highlighted code (purple border)
- **Tool Output**: Results from tool execution (yellow border)
- **Errors**: Error messages and warnings (red border)
- **System Messages**: Status and system information (gray border)

## Navigation

### Content Block Navigation

#### Basic Navigation
- `j` / `↓`: Move to next content block
- `k` / `↑`: Move to previous content block
- `g` + `g`: Go to first block
- `G`: Go to last block
- `Ctrl+d`: Scroll down half page
- `Ctrl+u`: Scroll up half page

#### Block Selection
- `Space`: Select/deselect current block
- `Shift+j/k`: Extend selection
- `v`: Enter visual selection mode
- `V`: Enter line selection mode
- `Ctrl+a`: Select all blocks

#### Block Actions
- `Enter`: Interact with selected block
- `y`: Copy selected block(s)
- `d`: Delete selected block(s)
- `r`: Reply to selected message
- `e`: Edit selected block (if editable)

### Search and Navigation
- `/`: Search within content blocks
- `n`: Next search result
- `N`: Previous search result
- `*`: Search for word under cursor
- `?`: Search backwards

## Input System

### Basic Input

#### Modes
- **Normal Mode**: Navigation and commands (default)
- **Insert Mode**: Text input and editing
- **Visual Mode**: Block selection and manipulation

#### Mode Switching
- `i`: Enter insert mode
- `Esc`: Return to normal mode
- `v`: Enter visual mode
- `:`: Enter command mode

### REPL Features

#### Multi-line Input
- `Shift+Enter`: Add new line without sending
- `Ctrl+Enter`: Send message (alternative to Enter)
- `Tab`: Auto-indent current line
- `Shift+Tab`: Unindent current line

#### Command History
- `↑`: Previous command in history
- `↓`: Next command in history
- `Ctrl+r`: Reverse search through history
- `Ctrl+s`: Forward search through history

### Auto-completion

#### Trigger Characters
- `@`: Mention completion (files, tools, contexts)
- `/`: Command completion (system and user commands)
- `.`: Context completion (current file/project)
- `Tab`: General completion

#### Navigation
- `Tab`: Accept current completion
- `↑/↓`: Navigate completion list
- `Ctrl+n/p`: Next/previous completion
- `Esc`: Cancel completion

#### Completion Types
- **File Mentions**: `@filename.rs` - Reference files
- **Tool Mentions**: `@tool_name` - Reference available tools
- **Commands**: `/help`, `/clear`, `/config` - System commands
- **Context**: `.function`, `.class` - Current file context

## Vim Keybindings

### Normal Mode Commands

#### Motion Commands
- `h/j/k/l`: Left/down/up/right
- `w/b`: Next/previous word
- `e`: End of word
- `0`: Beginning of line
- `$`: End of line
- `gg/G`: First/last line

#### Operators
- `d`: Delete (with motion)
- `y`: Yank/copy (with motion)
- `c`: Change (with motion)
- `dd/yy/cc`: Delete/yank/change line

#### Text Objects
- `iw/aw`: Inner/around word
- `i"/a"`: Inner/around quotes
- `i(/a(`: Inner/around parentheses
- `i{/a{`: Inner/around braces

### Insert Mode
- `Ctrl+w`: Delete word backwards
- `Ctrl+u`: Delete to beginning of line
- `Ctrl+o`: Execute one normal mode command
- `Ctrl+r`: Insert from register

### Visual Mode
- `o`: Move to other end of selection
- `U`: Uppercase selection
- `u`: Lowercase selection
- `>/<`: Indent/unindent selection

## Customization

### Themes

#### Built-in Themes
- **Default**: Standard color scheme
- **Dark**: Dark mode theme
- **Light**: Light mode theme
- **High Contrast**: Accessibility-focused theme
- **Solarized**: Popular Solarized color scheme
- **Monokai**: Monokai color scheme

#### Switching Themes
```bash
# Command mode
:theme dark
:theme solarized

# Configuration file
echo 'theme = "dark"' >> ~/.config/autorun/ui.toml
```

### Keybinding Customization

#### Configuration File: `~/.config/autorun/keybindings.toml`
```toml
[global]
quit = "q"
help = "?"

[normal_mode]
enter_insert = "i"
scroll_down = "j"
scroll_up = "k"
search = "/"

[insert_mode]
exit_insert = "Escape"
complete = "Tab"
new_line = "Shift+Enter"

[visual_mode]
copy = "y"
delete = "d"
```

### UI Configuration

#### Configuration File: `~/.config/autorun/ui.toml`
```toml
# Theme settings
theme = "default"
font_size = 14
line_numbers = true

# Behavior settings
vim_mode = true
auto_completion = true
syntax_highlighting = true

# Performance settings
max_history = 1000
virtual_scrolling = true
```

## Advanced Features

### Widget Integration

#### Widget Commands
- `:widget list`: Show available widgets
- `:widget add <type>`: Add widget to sidebar
- `:widget remove <id>`: Remove widget
- `w`: Focus widget area (from normal mode)

#### Widget Navigation
- `Tab`: Cycle through widgets
- `Shift+Tab`: Reverse cycle through widgets
- `Enter`: Interact with focused widget
- `Esc`: Return focus to main area

### Tool Integration

#### Tool Execution
- `@tool_name`: Mention and configure tool
- `:tool list`: List available tools
- `:tool help <name>`: Get tool help
- `:tool config <name>`: Configure tool

#### MCP Tools
- Tools from MCP servers appear in completion
- Use full tool names: `@server_name/tool_name`
- Configure MCP servers: `:mcp add <server>`

### Session Management

#### Session Commands
- `:session save <name>`: Save current session
- `:session load <name>`: Load saved session
- `:session list`: List saved sessions
- `:session delete <name>`: Delete session

#### Auto-save
- Sessions auto-save every 5 minutes
- Recovery available after unexpected exit
- Configure auto-save interval in settings

## Accessibility

### Screen Reader Support

#### Setup
1. Enable screen reader mode: `:set screen_reader=true`
2. Configure announcement level: `:set announce_level=verbose`
3. Set focus indicators: `:set focus_visible=true`

#### Navigation
- All content is announced when focused
- Block types are clearly identified
- Status changes are announced
- Keyboard shortcuts are announced

### High Contrast Mode

#### Enabling
```bash
# Command mode
:theme high_contrast

# Configuration
echo 'theme = "high_contrast"' >> ~/.config/autorun/ui.toml
```

#### Features
- High contrast colors for better visibility
- Bold borders and indicators
- Clear focus indicators
- Reduced visual noise

### Keyboard-only Navigation

#### Full Keyboard Support
- All features accessible via keyboard
- No mouse required for any functionality
- Logical tab order throughout interface
- Skip links for quick navigation

## Troubleshooting

### Common Issues

#### Performance Issues
- Reduce history size: `:set max_history=500`
- Disable syntax highlighting: `:set syntax_highlighting=false`
- Enable virtual scrolling: `:set virtual_scrolling=true`

#### Display Issues
- Reset theme: `:theme default`
- Clear cache: `:clear cache`
- Restart with clean config: `cargo run -- --clean-config`

#### Input Issues
- Reset keybindings: `:keybindings reset`
- Disable vim mode: `:set vim_mode=false`
- Check terminal compatibility: `:debug terminal`

### Getting Help

#### Built-in Help
- `?`: Show help overlay
- `:help`: Comprehensive help system
- `:help <topic>`: Topic-specific help
- `:keybindings`: Show current keybindings

#### Debug Information
- `:debug`: Show debug information
- `:version`: Show version information
- `:config`: Show current configuration
- `:log`: Show recent log entries

## Tips and Tricks

### Productivity Tips
1. **Use block selection** for bulk operations
2. **Customize keybindings** for your workflow
3. **Use command history** to repeat common tasks
4. **Set up themes** for different environments
5. **Use widgets** for frequently accessed tools

### Advanced Usage
1. **Combine operators and motions** in Vim mode
2. **Use registers** for complex copy/paste operations
3. **Create custom commands** for repetitive tasks
4. **Use search** to quickly find specific content
5. **Configure auto-completion** for your tools
