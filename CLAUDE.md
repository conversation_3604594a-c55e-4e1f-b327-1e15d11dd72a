# CLAUDE.md

## Build and Development Commands

### Basic Commands
```bash
# Build the project
cargo build

# Run the application (interactive TUI mode)
cargo run

# Run with debug logging
cargo run -- --debug

# Run tests
cargo test

# Run tests with verbose output
cargo test -- --nocapture

# Format code
cargo fmt

# Lint code
cargo clippy

# Check code without building
cargo check

# Run with console logging forced in TUI mode (useful for debugging)
AUTORUN_FORCE_CONSOLE_LOG=1 cargo run
```

### Non-Interactive Mode
```bash
# Run with a prompt (text output)
cargo run -- --print "your prompt here"

# JSON output format
cargo run -- --print "your prompt" --output-format json

# Streaming JSON output
cargo run -- --print "your prompt" --output-format stream-json
```

### Subcommands
```bash
# Configuration management
cargo run -- config get <key>
cargo run -- config set <key> <value>
cargo run -- config list

# MCP server management
cargo run -- mcp add <name> <command>
cargo run -- mcp list
cargo run -- mcp serve

# Health checks and updates
cargo run -- doctor
cargo run -- update
```

## LLM Provider Auto-Detection

AutoRun automatically detects your LLM provider based on available environment variables:

### Automatic Detection Priority:
1. **Explicit Override**: `AUTORUN_LLM_PROVIDER` (anthropic|openrouter)
2. **OpenRouter**: If `OPENROUTER_API_KEY` is set
3. **Anthropic**: If `ANTHROPIC_API_KEY` is set
4. **Default**: Falls back to Anthropic

### Environment Variables:
```bash
# For OpenRouter (automatically detected)
export OPENROUTER_API_KEY="your-openrouter-key"
export OPENROUTER_MODEL="anthropic/claude-3-sonnet"  # optional

# For Anthropic (automatically detected)
export ANTHROPIC_API_KEY="your-anthropic-key"

# Manual override (takes precedence over auto-detection)
export AUTORUN_LLM_PROVIDER="openrouter"
export OPENROUTER_API_KEY="your-key"
```

### Model Selection Examples:
```bash
# Use OpenRouter with auto-detection
export OPENROUTER_API_KEY="sk-or-..."
cargo run

# Use specific model with provider override
export AUTORUN_LLM_PROVIDER="openrouter"
cargo run -- --model "anthropic/claude-3-sonnet"
```

## Logging and Debugging

AutoRun uses a dual-layer logging system to prevent TUI interference:

### Log Output
- **File Logs**: Always written to `logs/autorun-YYYYMMDD-HHMMSS.log` in JSON format
- **Console Logs**: Automatically suppressed in TUI mode to prevent UI corruption
  - All console output is redirected to a null writer in TUI mode
  - Enabled in non-interactive mode (`--print`)
  - Can be forced in TUI mode with `AUTORUN_FORCE_CONSOLE_LOG=1` (will break UI)

### Viewing Logs
```bash
# Use the log viewer tool (recommended for TUI debugging)
./target/debug/log_viewer -- --debug

# Or tail the log file directly
tail -f logs/autorun-*.log | jq '.'

# Or use your external log monitoring
./scripts/monitor_agent_logs.sh
```

## Architecture Overview

### Core Design
AutoRun is an agentic coding assistant built as a high-performance Rust CLI with TUI interface. The architecture follows a modular async-first design:

- **TUI Framework**: Ratatui with Crossterm for cross-platform terminal UI
- **Async Runtime**: Tokio for concurrent operations between UI, LLM calls, and tool execution
- **MCP Protocol**: Model Context Protocol integration for extensible tool ecosystem via `rmcp` crate
- **Permission System**: Multi-layered safety for tool execution
- **Session Management**: Persistent conversation state with UUID tracking

### Module Structure
- **`agent/`**: AI agent core with conversation orchestration and LLM interaction patterns
- **`tools/`**: Tool trait definitions, execution context, registry, and built-in tools (file I/O, etc.)
- **`mcp/`**: MCP server/client implementation for external tool integration
- **`ui/`**: Ratatui-based TUI with message rendering, input handling, and status display
- **`llm/`**: Language model client abstractions and API integrations
- **`session/`**: Conversation persistence and state management
- **`config/`**: Configuration management for user preferences and tool settings

### Key Patterns
1. **Async Message Passing**: `mpsc::channel` for UI ↔ Agent communication via `AppMessage` enum
2. **Tool Abstraction**: `Tool` trait with async execution, permission checking, and JSON schema validation
3. **Execution Context**: `ExecutionContext` carries permissions, working directory, and session state
4. **Error Handling**: `thiserror` for structured errors with `AutorunError` and `ToolError` hierarchies

### Application Modes
- **Interactive TUI**: Default mode with real-time chat interface (Normal/Input modes)
- **Non-Interactive CLI**: Batch processing with text/JSON/streaming JSON output formats
- **MCP Server**: Standalone server mode for external tool integration

### Tool System
The tool system supports both built-in tools and MCP-based external tools:
- Built-in tools implement the `Tool` trait directly
- MCP tools integrate via the rmcp client with transport-agnostic communication
- All tools go through permission validation and execution context management
- Tool registry manages available tools and their capabilities

### Development Notes
- Uses standard Rust tooling without custom configuration
- Release builds are optimized for size (`opt-level = "z"`, LTO enabled)
- Panic handler restores terminal state in TUI mode
- Concurrent data structures via `dashmap` for thread-safe operations